{"cells": [{"cell_type": "markdown", "id": "9d9b5c91-f3a3-4709-a36f-40ecc86595d6", "metadata": {}, "source": ["# Forecasting future price trends by projecting historical price patterns"]}, {"cell_type": "markdown", "id": "2cffb873-e431-44f3-b243-e35969bbd2c1", "metadata": {}, "source": ["In our previous newsletter focusing on VectorBT PRO (VBT), we dived into the pattern detection capabilities of this powerful library. An additional key functionality is VBT's capacity to extrapolate identified price segments into the future and aggregate them for statistical analysis. This feature can be an invaluable tool for real-time decision-making in market analysis."]}, {"cell_type": "markdown", "id": "c472968b-1863-4d79-a299-ec67c1757455", "metadata": {}, "source": ["## Imports and set up"]}, {"cell_type": "markdown", "id": "ddf68612-622b-4803-87fc-a1ad80341536", "metadata": {}, "source": ["Given the self-contained design of VBT, a single import suffices."]}, {"cell_type": "code", "execution_count": 1, "id": "a42ccb91-bc73-4ad5-9327-18c7c22af598", "metadata": {}, "outputs": [], "source": ["from vectorbtpro import *\n", "# whats_imported()\n", "\n", "vbt.settings.set_theme(\"dark\")"]}, {"cell_type": "markdown", "id": "15412fda-c27f-4820-9273-17366164b2b3", "metadata": {}, "source": ["Let's define a set of variables for our analysis."]}, {"cell_type": "code", "execution_count": 2, "id": "fc016fe0-5ae6-416f-bb4d-84a33a91fce8", "metadata": {}, "outputs": [], "source": ["SYMBOL = \"BTCUSDT\"\n", "TIMEFRAME = \"1 hour\"\n", "START = \"one year ago\"\n", "\n", "LAST_N_BARS = 24\n", "PRED_N_BARS = 12\n", "\n", "GIF_FNAME = \"projections.gif\"\n", "GIF_N_BARS = 72\n", "GIF_FPS = 4\n", "GIF_PAD = 0.01"]}, {"cell_type": "markdown", "id": "e4667d70-f1d9-4f34-81ff-fdf8320477ae", "metadata": {}, "source": ["We will execute the analysis using price data retrieved from BinanceData, based on the parameters we previously defined."]}, {"cell_type": "code", "execution_count": 3, "id": "b797e0ff-320d-456b-91df-1e0e369d83a9", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0b6c7c645ad8470f85035d6c6ab8d670", "version_major": 2, "version_minor": 0}, "text/plain": ["0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = vbt.BinanceData.pull(SYMBOL, timeframe=TIMEFRAME, start=START)"]}, {"cell_type": "markdown", "id": "43fade8d-2d1f-492b-88bb-95facd21ceda", "metadata": {}, "source": ["## Find and plot projections"]}, {"cell_type": "markdown", "id": "0013fab2-d1fa-4777-99e9-2081a90444e3", "metadata": {}, "source": ["Let's write a function that analyzes the most recent price trend and employs it as a pattern to identify similar price movements in historical data. This pattern recognition function will focus exclusively on segments of price history having a comparable percentage change from their respective starting points."]}, {"cell_type": "code", "execution_count": 4, "id": "f7f4ead3-c4db-47d5-8a30-3f7dbe4347dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n"]}], "source": ["def find_patterns(data):\n", "    price = data.hlc3\n", "    pattern = price.values[-LAST_N_BARS:]\n", "    pattern_ranges = price.vbt.find_pattern(\n", "        pattern=pattern,\n", "        rescale_mode=\"rebase\",\n", "        overlap_mode=\"allow\",\n", "        wrapper_kwargs=dict(freq=TIMEFRAME)\n", "    )\n", "    pattern_ranges = pattern_ranges.status_closed\n", "    return pattern_ranges\n", "\n", "pattern_ranges = find_patterns(data)\n", "print(pattern_ranges.count())"]}, {"cell_type": "markdown", "id": "6dc1f00c-f0a2-4b74-831f-3043c14f1195", "metadata": {}, "source": ["We have identified a number of price segments that closely resemble the latest price trend. Now, we'll write a function that extracts the price data immediately succeeding each identified segment and plots these as extensions of the price trend. These subsequent segments are known as \"projections.\""]}, {"cell_type": "code", "execution_count": 5, "id": "9fb7b02c-190a-488e-bfa6-843db23c324e", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(33, 34, 38); fill-opacity: 1;\"/><defs id=\"defs-df572f\"><g class=\"clips\"><clipPath id=\"clipdf572fxyplot\" class=\"plotclip\"><rect width=\"623\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipdf572fx\"><rect x=\"47\" y=\"0\" width=\"623\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipdf572fy\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipdf572fxy\"><rect x=\"47\" y=\"46\" width=\"623\" height=\"261\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"minor-gridlayer\"><g class=\"x\"/><g class=\"y\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(108.42,0)\" d=\"M0,46v261\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(213.72,0)\" d=\"M0,46v261\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(319.01,0)\" d=\"M0,46v261\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(424.31,0)\" d=\"M0,46v261\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(529.61,0)\" d=\"M0,46v261\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(634.9,0)\" d=\"M0,46v261\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,281.76)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,219.81)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,157.86)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,95.91)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(47,46)\" clip-path=\"url(#clipdf572fxyplot)\"><g class=\"boxlayer mlayer\"><g class=\"trace boxes\" style=\"opacity: 0.5;\"><path class=\"box\" d=\"M4.48,107.27H13.07M4.48,119.84H13.07V106.27H4.48ZM8.77,119.84V129.07M8.77,106.27V97.64\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M22.02,109.8H30.62M22.02,110.8H30.62V106.27H22.02ZM26.32,110.8V118.67M26.32,106.27V101.68\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M39.57,124.31H48.17M39.57,125.31H48.17V110.8H39.57ZM43.87,125.31V125.5M43.87,110.8V107.89\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M57.12,117.2H65.72M57.12,125.31H65.72V116.2H57.12ZM61.42,125.31V127.06M61.42,116.2V112.48\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M74.67,120.23H83.27M74.67,121.23H83.27V116.2H74.67ZM78.97,121.23V122.62M78.97,116.2V110.62\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M92.22,115.36H100.82M92.22,121.23H100.82V114.36H92.22ZM96.52,121.23V128.6M96.52,114.36V113.62\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M109.77,115.36H118.37M109.77,115.44H118.37V114.36H109.77ZM114.07,115.44V127.28M114.07,114.36V101.38\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M127.32,123.23H135.92M127.32,124.23H135.92V115.44H127.32ZM131.62,124.23V124.23M131.62,115.44V107.64\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M144.87,146.34H153.47M144.87,147.34H153.47V124.23H144.87ZM149.17,147.34V163.34M149.17,124.23V122.24\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M162.42,141.11H171.02M162.42,147.35H171.02V140.11H162.42ZM166.72,147.35V152.5M166.72,140.11V137.28\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M179.97,138.4H188.57M179.97,140.11H188.57V137.4H179.97ZM184.27,140.11V147.23M184.27,137.4V128.7\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M197.52,142.49H206.12M197.52,143.49H206.12V137.4H197.52ZM201.82,143.49V146.96M201.82,137.4V135.08\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M215.07,132.02H223.67M215.07,143.49H223.67V131.02H215.07ZM219.37,143.49V146.92M219.37,131.02V131.02\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M232.62,120.67H241.22M232.62,131.02H241.22V119.67H232.62ZM236.92,131.02V133.63M236.92,119.67V116.2\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M250.17,125.18H258.76M250.17,126.18H258.76V119.66H250.17ZM254.46,126.18V126.69M254.46,119.66V117.23\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M267.71,123.87H276.31M267.71,126.18H276.31V122.87H267.71ZM272.01,126.18V128.85M272.01,122.87V121.15\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M285.26,117.02H293.86M285.26,122.87H293.86V116.02H285.26ZM289.56,122.87V128.46M289.56,116.02V109.88\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M302.81,138.67H311.41M302.81,139.67H311.41V116.02H302.81ZM307.11,139.67V144.81M307.11,116.02V105.79\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M320.36,159.58H328.96M320.36,160.58H328.96V139.67H320.36ZM324.66,160.58V160.94M324.66,139.67V134.41\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M337.91,150.03H346.51M337.91,160.58H346.51V149.03H337.91ZM342.21,160.58V160.75M342.21,149.03V146.86\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M355.46,145.32H364.06M355.46,149.03H364.06V144.32H355.46ZM359.76,149.03V150.39M359.76,144.32V141.84\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M373.01,127.23H381.61M373.01,144.32H381.61V126.23H373.01ZM377.31,144.32V145.08M377.31,126.23V124.25\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M390.56,131.35H399.16M390.56,132.35H399.16V126.23H390.56ZM394.86,132.35V143.58M394.86,126.23V123.46\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M408.11,130.8H416.71M408.11,132.35H416.71V129.8H408.11ZM412.41,132.35V135.55M412.41,129.8V129.45\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/></g></g><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace240c3b5c-e75a-476e-8e1b-3ab4e6cbbe99\" style=\"stroke-miterlimit: 2; opacity: 0.5;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,129.8L429.96,122.12L447.51,117.95L465.06,13.05L482.61,67.96L500.15,82.95L517.7,91.35L535.25,83.49L552.8,101.44L570.35,105.61L587.9,106.83L605.45,102.6L623,95.41\" style=\"vector-effect: none; fill: none; stroke: rgb(106, 172, 39); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace522fcf31-c071-4c7d-ba53-98e96514364e\" style=\"stroke-miterlimit: 2; opacity: 0.5;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,129.8L429.96,81.16L447.51,88.24L465.06,74.31L482.61,82.82L500.15,73.76L517.7,78.16L535.25,61.1L552.8,76.26L570.35,80.46L587.9,82.55L605.45,79.83L623,67.86\" style=\"vector-effect: none; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter traceb005fcd1-fe87-4a72-8f5b-f521796b1fbe\" style=\"stroke-miterlimit: 2; opacity: 0.5;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,129.8L429.96,136.82L447.51,123L465.06,131.44L482.61,122.46L500.15,126.83L517.7,109.92L535.25,124.94L552.8,129.1L570.35,131.17L587.9,128.47L605.45,116.61L623,123.13\" style=\"vector-effect: none; fill: none; stroke: rgb(179, 187, 34); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace570d484c-4b3c-4d2b-9ee9-5c9f8aed4791\" style=\"stroke-miterlimit: 2; opacity: 0.5;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,129.8L429.96,143.35L447.51,29.4L465.06,50.73L482.61,34.97L500.15,62.73L517.7,114.53L535.25,96.8L552.8,100.89L570.35,85.46L587.9,104.06L605.45,117.82L623,147.87\" style=\"vector-effect: none; fill: none; stroke: rgb(103, 172, 39); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace3ed2a77c-6e1e-492c-8005-6e7dd5f5fc51\" style=\"stroke-miterlimit: 2; opacity: 0.5;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,129.8L429.96,15.56L447.51,36.94L465.06,21.15L482.61,48.98L500.15,100.9L517.7,83.12L535.25,87.23L552.8,71.76L570.35,90.4L587.9,104.2L605.45,134.33L623,136.2\" style=\"vector-effect: none; fill: none; stroke: rgb(64, 164, 42); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace1aa72a1e-a7b0-4009-b597-076736384930\" style=\"stroke-miterlimit: 2; opacity: 0.5;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,129.8L429.96,150.74L447.51,135.27L465.06,162.53L482.61,213.38L500.15,195.97L517.7,199.99L535.25,184.84L552.8,203.1L570.35,216.61L587.9,246.12L605.45,247.95L623,237.88\" style=\"vector-effect: none; fill: none; stroke: rgb(220, 57, 18); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(108.42,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">18:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 9, 2023</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(213.72,0)\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 10, 2023</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(319.01,0)\">06:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(424.31,0)\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(529.61,0)\">18:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(634.9,0)\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 11, 2023</tspan></text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" transform=\"translate(0,281.76)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">43k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,219.81)\">43.5k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,157.86)\">44k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,95.91)\">44.5k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"smithlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-df572f\"><g class=\"clips\"/><clipPath id=\"legenddf572f\"><rect width=\"171\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(499,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(33, 34, 38); fill-opacity: 1; stroke-width: 0px;\" width=\"171\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legenddf572f)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 0.5;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendcandle\" d=\"M15,0H8M8,-6V6H-8Z\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; stroke-width: 2px; fill: rgb(238, 83, 79); fill-opacity: 1; stroke: rgb(238, 83, 79); stroke-opacity: 1;\"/><path class=\"legendcandle\" d=\"M-15,0H-8M-8,6V-6H8Z\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; stroke-width: 2px; fill: rgb(38, 166, 154); fill-opacity: 1; stroke: rgb(38, 166, 154); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(78.53125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">proj (6)</text><g class=\"layers\" style=\"opacity: 0.5;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(106, 172, 39); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"89.265625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_projections(data, pattern_ranges, **kwargs):\n", "    projection_ranges = pattern_ranges.with_delta(\n", "        PRED_N_BARS,\n", "        open=data.open,\n", "        high=data.high,\n", "        low=data.low,\n", "        close=data.close,\n", "    )\n", "    projection_ranges = projection_ranges.status_closed\n", "    return projection_ranges.plot_projections(\n", "        plot_past_period=LAST_N_BARS, \n", "        **kwargs,\n", "    )\n", "\n", "plot_projections(data, pattern_ranges, plot_bands=False).show_svg()"]}, {"cell_type": "markdown", "id": "8df73436-c6ae-411b-8c44-e5764f9c1812", "metadata": {}, "source": ["As we can see, similar price movements have historically branched into a diverse set of trajectories. For a visually compelling and statistically robust forecast, we will display the confidence bands encompassing all the projections, with 60% of these projections falling between the upper and lower bands."]}, {"cell_type": "code", "execution_count": 6, "id": "b97458a5-7428-4877-80c6-a522aef4b5ce", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(33, 34, 38); fill-opacity: 1;\"/><defs id=\"defs-836681\"><g class=\"clips\"><clipPath id=\"clip836681xyplot\" class=\"plotclip\"><rect width=\"623\" height=\"242\"/></clipPath><clipPath class=\"axesclip\" id=\"clip836681x\"><rect x=\"47\" y=\"0\" width=\"623\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip836681y\"><rect x=\"0\" y=\"65\" width=\"700\" height=\"242\"/></clipPath><clipPath class=\"axesclip\" id=\"clip836681xy\"><rect x=\"47\" y=\"65\" width=\"623\" height=\"242\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"minor-gridlayer\"><g class=\"x\"/><g class=\"y\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(108.42,0)\" d=\"M0,65v242\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(213.72,0)\" d=\"M0,65v242\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(319.01,0)\" d=\"M0,65v242\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(424.31,0)\" d=\"M0,65v242\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(529.61,0)\" d=\"M0,65v242\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(634.9,0)\" d=\"M0,65v242\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,283.6)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,226.16)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,168.72)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,111.28)\" d=\"M47,0h623\" style=\"stroke: rgb(49, 52, 57); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(47,65)\" clip-path=\"url(#clip836681xyplot)\"><g class=\"boxlayer mlayer\"><g class=\"trace boxes\" style=\"opacity: 0.5;\"><path class=\"box\" d=\"M4.48,99.54H13.07M4.48,111.12H13.07V98.54H4.48ZM8.77,111.12V119.67M8.77,98.54V90.53\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M22.02,101.73H30.62M22.02,102.73H30.62V98.54H22.02ZM26.32,102.73V110.03M26.32,98.54V94.27\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M39.57,115.19H48.17M39.57,116.19H48.17V102.73H39.57ZM43.87,116.19V116.37M43.87,102.73V100.04\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M57.12,108.74H65.72M57.12,116.19H65.72V107.74H57.12ZM61.42,116.19V117.81M61.42,107.74V104.29\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M74.67,111.4H83.27M74.67,112.4H83.27V107.74H74.67ZM78.97,112.4V113.69M78.97,107.74V102.56\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M92.22,107.04H100.82M92.22,112.4H100.82V106.04H92.22ZM96.52,112.4V119.24M96.52,106.04V105.35\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M109.77,107.03H118.37M109.77,107.03H118.37V106.04H109.77ZM114.07,107.03V118.02M114.07,106.04V94\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M127.32,114.19H135.92M127.32,115.19H135.92V107.04H127.32ZM131.62,115.19V115.19M131.62,107.04V99.81\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M144.87,135.62H153.47M144.87,136.62H153.47V115.19H144.87ZM149.17,136.62V151.45M149.17,115.19V113.34\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M162.42,130.91H171.02M162.42,136.62H171.02V129.91H162.42ZM166.72,136.62V141.4M166.72,129.91V127.29\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M179.97,128.4H188.57M179.97,129.91H188.57V127.4H179.97ZM184.27,129.91V136.52M184.27,127.4V119.34\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M197.52,132.04H206.12M197.52,133.04H206.12V127.4H197.52ZM201.82,133.04V136.26M201.82,127.4V125.25\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M215.07,122.49H223.67M215.07,133.05H223.67V121.49H215.07ZM219.37,133.05V136.23M219.37,121.49V121.48\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M232.62,111.95H241.22M232.62,121.49H241.22V110.95H232.62ZM236.92,121.49V123.9M236.92,110.95V107.74\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M250.17,116H258.76M250.17,117H258.76V110.95H250.17ZM254.46,117V117.46M254.46,110.95V108.7\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M267.71,114.93H276.31M267.71,117H276.31V113.93H267.71ZM272.01,117V119.47M272.01,113.93V112.33\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M285.26,108.57H293.86M285.26,113.93H293.86V107.57H285.26ZM289.56,113.93V119.11M289.56,107.57V101.88\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M302.81,128.51H311.41M302.81,129.51H311.41V107.57H302.81ZM307.11,129.51V134.27M307.11,107.57V98.09\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M320.36,147.89H328.96M320.36,148.89H328.96V129.51H320.36ZM324.66,148.89V149.22M324.66,129.51V124.62\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M337.91,139.18H346.51M337.91,148.89H346.51V138.18H337.91ZM342.21,148.89V149.05M342.21,138.18V136.17\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M355.46,134.81H364.06M355.46,138.18H364.06V133.81H355.46ZM359.76,138.18V139.44M359.76,133.81V131.52\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M373.01,118.04H381.61M373.01,133.81H381.61V117.04H373.01ZM377.31,133.81V134.52M377.31,117.04V115.2\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M390.56,121.71H399.16M390.56,122.71H399.16V117.04H390.56ZM394.86,122.71V133.13M394.86,117.04V114.48\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(238, 83, 79); stroke-opacity: 1; fill: rgb(238, 83, 79); fill-opacity: 1; opacity: 1;\"/><path class=\"box\" d=\"M408.11,121.35H416.71M408.11,122.71H416.71V120.35H408.11ZM412.41,122.71V125.68M412.41,120.35V120.03\" style=\"vector-effect: none; stroke-width: 2px; stroke: rgb(38, 166, 154); stroke-opacity: 1; fill: rgb(38, 166, 154); fill-opacity: 1; opacity: 1;\"/></g></g><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace3dd08a5a-f270-427a-9644-1d59d76d159b\" style=\"stroke-miterlimit: 2; opacity: 0.1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,113.23L447.51,109.37L465.06,12.1L482.61,63.01L500.15,76.92L517.7,84.7L535.25,77.41L552.8,94.06L570.35,97.92L587.9,99.06L605.45,95.13L623,88.46\" style=\"vector-effect: none; fill: none; stroke: rgb(106, 172, 39); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace140a7b69-d102-4eff-9de7-bbe25aa49a9a\" style=\"stroke-miterlimit: 2; opacity: 0.1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,75.25L447.51,81.82L465.06,68.9L482.61,76.79L500.15,68.39L517.7,72.47L535.25,56.66L552.8,70.71L570.35,74.6L587.9,76.54L605.45,74.02L623,62.92\" style=\"vector-effect: none; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace9c9984c0-0d58-4b9a-b71e-99e19d05ce4d\" style=\"stroke-miterlimit: 2; opacity: 0.1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,126.86L447.51,114.05L465.06,121.87L482.61,113.55L500.15,117.59L517.7,101.91L535.25,115.84L552.8,119.7L570.35,121.62L587.9,119.12L605.45,108.12L623,114.16\" style=\"vector-effect: none; fill: none; stroke: rgb(179, 187, 34); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace0d5d2d73-d1cb-4e8a-8301-9d3c85c58a91\" style=\"stroke-miterlimit: 2; opacity: 0.1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,132.92L447.51,27.26L465.06,47.04L482.61,32.43L500.15,58.17L517.7,106.19L535.25,89.75L552.8,93.55L570.35,79.24L587.9,96.48L605.45,109.24L623,137.11\" style=\"vector-effect: none; fill: none; stroke: rgb(103, 172, 39); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracebd4016dd-2b35-4968-bdaa-f395e583801a\" style=\"stroke-miterlimit: 2; opacity: 0.1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,14.43L447.51,34.25L465.06,19.61L482.61,45.41L500.15,93.55L517.7,77.07L535.25,80.88L552.8,66.54L570.35,83.82L587.9,96.61L605.45,124.55L623,126.28\" style=\"vector-effect: none; fill: none; stroke: rgb(64, 164, 42); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace9ecb8f92-ed26-4118-a82b-07114bf4d826\" style=\"stroke-miterlimit: 2; opacity: 0.1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,139.76L447.51,125.42L465.06,150.69L482.61,197.85L500.15,181.7L517.7,185.43L535.25,171.38L552.8,188.32L570.35,200.84L587.9,228.2L605.45,229.9L623,220.57\" style=\"vector-effect: none; fill: none; stroke: rgb(220, 57, 18); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracec42693c8-aed8-464c-861f-2416b4180340\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"><g><path class=\"js-fill\" d=\"M412.41,120.35L429.96,120.04L447.51,95.59L465.06,57.97L482.61,69.9L500.15,85.23L517.7,93.31L535.25,85.31L552.8,93.8L570.35,90.87L587.9,97.84L623,120.22L623,137.11L605.45,124.55L587.9,119.12L570.35,121.62L552.8,119.7L535.25,115.84L517.7,106.19L500.15,117.59L482.61,113.55L465.06,121.87L447.51,114.05L429.96,132.92L412.41,120.35Z\" style=\"fill: rgb(127, 127, 127); fill-opacity: 0.25; stroke-width: 0;\"/></g></g><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,132.92L447.51,114.05L465.06,121.87L482.61,113.55L500.15,117.59L517.7,106.19L535.25,115.84L552.8,119.7L570.35,121.62L587.9,119.12L605.45,124.55L623,137.11\" style=\"vector-effect: none; fill: none; stroke: rgb(186, 188, 34); stroke-opacity: 0.75; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace4ecc534f-e2ca-4ed5-890c-ef1fa353e461\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"><g><path class=\"js-fill\" d=\"M412.41,120.35L429.96,75.25L447.51,34.25L465.06,19.61L482.61,45.41L500.15,68.39L517.7,77.07L535.25,77.41L552.8,70.71L570.35,79.24L587.9,96.48L605.45,95.13L623,88.46L623,120.22L587.9,97.84L570.35,90.87L552.8,93.8L535.25,85.31L517.7,93.31L500.15,85.23L482.61,69.9L465.06,57.97L447.51,95.59L429.96,120.04L412.41,120.35Z\" style=\"fill: rgb(127, 127, 127); fill-opacity: 0.25; stroke-width: 0;\"/></g></g><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,120.04L447.51,95.59L465.06,57.97L482.61,69.9L500.15,85.23L517.7,93.31L535.25,85.31L552.8,93.8L570.35,90.87L587.9,97.84L623,120.22\" style=\"vector-effect: none; fill: none; stroke: rgb(104, 172, 39); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace91170562-93e7-4941-859a-c301befd26e1\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,75.25L447.51,34.25L465.06,19.61L482.61,45.41L500.15,68.39L517.7,77.07L535.25,77.41L552.8,70.71L570.35,79.24L587.9,96.48L605.45,95.13L623,88.46\" style=\"vector-effect: none; fill: none; stroke: rgb(53, 161, 43); stroke-opacity: 0.75; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracedaa557be-a0eb-4f7b-ad43-76e648cf92fb\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M412.41,120.35L429.96,100.41L447.51,82.03L465.06,70.03L482.61,88.17L500.15,99.39L517.7,104.63L535.25,98.65L552.8,105.48L570.35,109.67L587.9,119.34L605.45,123.49L623,124.92\" style=\"vector-effect: none; fill: none; stroke: rgb(138, 179, 37); stroke-opacity: 1; stroke-dasharray: 3px, 3px; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(108.42,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">18:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 9, 2023</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(213.72,0)\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 10, 2023</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(319.01,0)\">06:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(424.31,0)\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(529.61,0)\">18:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(634.9,0)\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 11, 2023</tspan></text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" transform=\"translate(0,283.6)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">43k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,226.16)\">43.5k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,168.72)\">44k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"46\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,111.28)\">44.5k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"smithlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-836681\"><g class=\"clips\"/><clipPath id=\"legend836681\"><rect width=\"527\" height=\"48\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(143,12.159999999999997)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(33, 34, 38); fill-opacity: 1; stroke-width: 0px;\" width=\"527\" height=\"48\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend836681)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 0.5;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendcandle\" d=\"M15,0H8M8,-6V6H-8Z\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; stroke-width: 2px; fill: rgb(238, 83, 79); fill-opacity: 1; stroke: rgb(238, 83, 79); stroke-opacity: 1;\"/><path class=\"legendcandle\" d=\"M-15,0H-8M-8,6V-6H8Z\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; stroke-width: 2px; fill: rgb(38, 166, 154); fill-opacity: 1; stroke: rgb(38, 166, 154); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(131.578125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">proj (6)</text><g class=\"layers\" style=\"opacity: 0.1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(106, 172, 39); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"89.265625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(263.15625,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Q=20% (proj)</text><g class=\"layers\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(186, 188, 34); stroke-opacity: 0.75; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"129.078125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(394.734375,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Q=50% (proj)</text><g class=\"layers\"><g class=\"legendfill\"><path class=\"js-fill\" d=\"M5,-2h30v6h-30z\" style=\"stroke-width: 0; fill: rgb(127, 127, 127); fill-opacity: 0.25;\"/></g><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,-2h30\" style=\"fill: none; stroke: rgb(104, 172, 39); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"129.078125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Q=80% (proj)</text><g class=\"layers\"><g class=\"legendfill\"><path class=\"js-fill\" d=\"M5,-2h30v6h-30z\" style=\"stroke-width: 0; fill: rgb(127, 127, 127); fill-opacity: 0.25;\"/></g><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,-2h30\" style=\"fill: none; stroke: rgb(53, 161, 43); stroke-opacity: 0.75; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"129.078125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(131.578125,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Mean (proj)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(138, 179, 37); stroke-opacity: 1; stroke-dasharray: 3px, 3px; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"113.703125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_projections(data, pattern_ranges, plot_bands=True).show_svg()"]}, {"cell_type": "markdown", "id": "9011e2c5-1745-480c-b9da-c031f6ba9ae2", "metadata": {}, "source": ["## Generate animation"]}, {"cell_type": "markdown", "id": "ac05a0ea-6883-4736-a815-619f76607966", "metadata": {}, "source": ["Lastly, we will compile a GIF animation that iterates through a specified range of bars, applying the aforementioned procedure to each bar within that range."]}, {"cell_type": "code", "execution_count": 14, "id": "6238530e-9d06-4da4-a71d-3ae7489c2c9a", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9513f546819a4f47ae9cf0289f0156ae", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/72 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_frame(frame_index, **kwargs):\n", "    sub_data = data.loc[:frame_index[-1]]\n", "    pattern_ranges = find_patterns(sub_data)\n", "    if pattern_ranges.count() < 3:\n", "        return None\n", "    return plot_projections(sub_data, pattern_ranges, **kwargs)\n", "\n", "vbt.save_animation(\n", "    GIF_FNAME,\n", "    data.index[-GIF_N_BARS:],\n", "    plot_frame,\n", "    plot_projections=False,\n", "    delta=1,\n", "    fps=GIF_FPS,\n", "    writer_kwargs=dict(loop=0),\n", "    yaxis_range=[\n", "        data.low.iloc[-GIF_N_BARS:].min() * (1 - GIF_PAD), \n", "        data.high.iloc[-GIF_N_BARS:].max() * (1 + GIF_PAD)\n", "    ],\n", ")"]}, {"cell_type": "markdown", "id": "91b825fb-7e4c-4d48-ae73-bffe633a6f52", "metadata": {}, "source": ["Bear in mind that while the confidence bands describe past performance, they should not be used as guarantees of future results."]}, {"cell_type": "code", "execution_count": null, "id": "319a24bb-e210-4d02-ab2c-0ce58b3dc82c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}