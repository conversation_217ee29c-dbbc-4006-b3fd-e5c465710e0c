# ==================================== VBTPROXYZ ====================================
# Copyright (c) 2021-2025 Oleg Polakow. All rights reserved.
#
# This file is part of the proprietary VectorBT® PRO package and is licensed under
# the VectorBT® PRO License available at https://vectorbt.pro/terms/software-license/
#
# Unauthorized publishing, distribution, sublicensing, or sale of this software
# or its parts is strictly prohibited.
# ===================================================================================

"""Module providing the `CSVData` class."""

from pathlib import Path

import numpy as np
import pandas as pd

from vectorbtpro import _typing as tp
from vectorbtpro.data.custom.file import FileData
from vectorbtpro.utils import datetime_ as dt
from vectorbtpro.utils.config import merge_dicts

__all__ = [
    "CSVData",
]

__pdoc__ = {}

CSVDataT = tp.TypeVar("CSVDataT", bound="CSVData")


class CSVData(FileData):
    """Class for fetching CSV and TSV data files.

    This class extends `FileData` and provides methods for verifying file types,
    resolving metadata keys, and reading CSV files using pandas.
    """

    _settings_path: tp.SettingsPath = dict(custom="data.custom.csv")

    @classmethod
    def is_csv_file(cls, path: tp.PathLike) -> bool:
        """Return whether the given path is a CSV or TSV file.

        Args:
            path (PathLike): File path to check.
        """
        if not isinstance(path, Path):
            path = Path(path)
        if path.exists() and path.is_file() and ".csv" in path.suffixes:
            return True
        if path.exists() and path.is_file() and ".tsv" in path.suffixes:
            return True
        return False

    @classmethod
    def is_file_match(cls, path: tp.PathLike) -> bool:
        return cls.is_csv_file(path)

    @classmethod
    def resolve_keys_meta(
        cls,
        keys: tp.Union[None, dict, tp.MaybeKeys] = None,
        keys_are_features: tp.Optional[bool] = None,
        features: tp.Union[None, dict, tp.MaybeFeatures] = None,
        symbols: tp.Union[None, dict, tp.MaybeSymbols] = None,
        paths: tp.Any = None,
    ) -> tp.Kwargs:
        keys_meta = FileData.resolve_keys_meta(
            keys=keys,
            keys_are_features=keys_are_features,
            features=features,
            symbols=symbols,
        )
        if keys_meta["keys"] is None and paths is None:
            keys_meta["keys"] = cls.list_paths()
        return keys_meta

    @classmethod
    def fetch_key(
        cls,
        key: tp.Key,
        path: tp.Any = None,
        start: tp.Optional[tp.DatetimeLike] = None,
        end: tp.Optional[tp.DatetimeLike] = None,
        tz: tp.TimezoneLike = None,
        start_row: tp.Optional[int] = None,
        end_row: tp.Optional[int] = None,
        header: tp.Optional[tp.MaybeSequence[int]] = None,
        index_col: tp.Optional[int] = None,
        parse_dates: tp.Optional[bool] = None,
        chunk_func: tp.Optional[tp.Callable] = None,
        squeeze: tp.Optional[bool] = None,
        **read_kwargs,
    ) -> tp.KeyData:
        """Fetch the CSV file for a given feature or symbol.

        Arguments `skiprows` and `nrows` are automatically determined based on `start_row` and `end_row`.

        If either `start` or `end` is provided, the entire CSV file is read before applying date filtering.

        For additional parameters, refer to `pd.read_csv` documentation.

        Args:
            key (hashable): The identifier for a feature or symbol.
            path (Any): File path to the CSV file.

                If not provided, `key` is used as the file path.
            start (Optional[DatetimeLike]): The starting datetime for filtering data.

                The object's timezone will be used. See `vectorbtpro.utils.datetime_.to_timestamp`.
            end (Optional[DatetimeLike]): The ending datetime for filtering data.

                The object's timezone will be used. See `vectorbtpro.utils.datetime_.to_timestamp`.
            tz (TimezoneLike): The target timezone for conversion.

                See `vectorbtpro.utils.datetime_.to_timezone`.
            start_row (Optional[int]): The starting row index (inclusive), excluding header rows.
            end_row (Optional[int]): The ending row index (exclusive), excluding header rows.
            header (Optional[Union[int, Sequence[int]]]): Row number(s) to use as the header.

                See `pd.read_csv` for details.
            index_col (Optional[int]): Column to use as the index.

                If set to False, it is treated as None. See `pd.read_csv`.
            parse_dates (Optional[bool]): Whether to parse dates.

                See `pd.read_csv`.
            chunk_func (Optional[Callable]): Function for processing chunks from a `TextFileReader`.

                This function is invoked when `iterator` or `chunksize` is specified.
            squeeze (Optional[bool]): If True, a DataFrame with a single column is squeezed into a Series.
            **read_kwargs: Keyword arguments passed to `pd.read_csv`.

        Returns:
            SymbolData: The fetched data and a metadata dictionary.

        Defaults are specified in `custom.csv` within `vectorbtpro._settings.data`.
        """
        from pandas.io.parsers import TextFileReader
        from pandas.api.types import is_object_dtype

        start = cls.resolve_custom_setting(start, "start")
        end = cls.resolve_custom_setting(end, "end")
        tz = cls.resolve_custom_setting(tz, "tz")
        start_row = cls.resolve_custom_setting(start_row, "start_row")
        if start_row is None:
            start_row = 0
        end_row = cls.resolve_custom_setting(end_row, "end_row")
        header = cls.resolve_custom_setting(header, "header")
        index_col = cls.resolve_custom_setting(index_col, "index_col")
        if index_col is False:
            index_col = None
        parse_dates = cls.resolve_custom_setting(parse_dates, "parse_dates")
        chunk_func = cls.resolve_custom_setting(chunk_func, "chunk_func")
        squeeze = cls.resolve_custom_setting(squeeze, "squeeze")
        read_kwargs = cls.resolve_custom_setting(read_kwargs, "read_kwargs", merge=True)

        if path is None:
            path = key
        if isinstance(header, int):
            header = [header]
        header_rows = header[-1] + 1
        start_row += header_rows
        if end_row is not None:
            end_row += header_rows
        skiprows = range(header_rows, start_row)
        if end_row is not None:
            nrows = end_row - start_row
        else:
            nrows = None

        sep = read_kwargs.pop("sep", None)
        if isinstance(path, (str, Path)):
            try:
                _path = Path(path)
                if _path.suffix.lower() == ".csv":
                    if sep is None:
                        sep = ","
                if _path.suffix.lower() == ".tsv":
                    if sep is None:
                        sep = "\t"
            except Exception as e:
                pass
        if sep is None:
            sep = ","

        obj = pd.read_csv(
            path,
            sep=sep,
            header=header,
            index_col=index_col,
            parse_dates=parse_dates,
            skiprows=skiprows,
            nrows=nrows,
            **read_kwargs,
        )
        if isinstance(obj, TextFileReader):
            if chunk_func is None:
                obj = pd.concat(list(obj), axis=0)
            else:
                obj = chunk_func(obj)
        if isinstance(obj, pd.DataFrame) and squeeze:
            obj = obj.squeeze("columns")
        if isinstance(obj, pd.Series) and obj.name == "0":
            obj.name = None
        if index_col is not None and parse_dates and is_object_dtype(obj.index.dtype):
            obj.index = pd.to_datetime(obj.index, utc=True)
            if tz is not None:
                obj.index = obj.index.tz_convert(tz)
        if isinstance(obj.index, pd.DatetimeIndex) and tz is None:
            tz = obj.index.tz
        if start is not None or end is not None:
            if not isinstance(obj.index, pd.DatetimeIndex):
                raise TypeError("Cannot filter index that is not DatetimeIndex")
            if obj.index.tz is not None:
                if start is not None:
                    start = dt.to_tzaware_timestamp(start, naive_tz=tz, tz=obj.index.tz)
                if end is not None:
                    end = dt.to_tzaware_timestamp(end, naive_tz=tz, tz=obj.index.tz)
            else:
                if start is not None:
                    start = dt.to_naive_timestamp(start, tz=tz)
                if end is not None:
                    end = dt.to_naive_timestamp(end, tz=tz)
            mask = True
            if start is not None:
                mask &= obj.index >= start
            if end is not None:
                mask &= obj.index < end
            mask_indices = np.flatnonzero(mask)
            if len(mask_indices) == 0:
                return None
            obj = obj.iloc[mask_indices[0] : mask_indices[-1] + 1]
            start_row += mask_indices[0]
        return obj, dict(last_row=start_row - header_rows + len(obj.index) - 1, tz=tz)

    @classmethod
    def fetch_feature(cls, feature: tp.Feature, **kwargs) -> tp.FeatureData:
        """Fetch the CSV file for a feature.

        Args:
            feature (hashable): The feature identifier.
            **kwargs: Keyword arguments to pass to `CSVData.fetch_key`.

        Returns:
            FeatureData: The fetched data and a metadata dictionary.
        """
        return cls.fetch_key(feature, **kwargs)

    @classmethod
    def fetch_symbol(cls, symbol: tp.Symbol, **kwargs) -> tp.SymbolData:
        """Fetch the CSV file for a symbol.

        Args:
            symbol (hashable): The symbol identifier.
            **kwargs: Keyword arguments to pass to `CSVData.fetch_key`.

        Returns:
            SymbolData: The fetched data and a metadata dictionary.
        """
        return cls.fetch_key(symbol, **kwargs)

    def update_key(self, key: tp.Key, key_is_feature: bool = False, **kwargs) -> tp.KeyData:
        """Update the CSV data for a feature or symbol.

        Args:
            key (hashable): The identifier for the feature or symbol.
            key_is_feature (bool): Indicates whether `key` represents a feature.
            **kwargs: Keyword arguments for data fetching operations.

        Returns:
            KeyData: The updated data and a metadata dictionary.
        """
        fetch_kwargs = self.select_fetch_kwargs(key)
        returned_kwargs = self.select_returned_kwargs(key)
        fetch_kwargs["start_row"] = returned_kwargs["last_row"]
        kwargs = merge_dicts(fetch_kwargs, kwargs)
        if key_is_feature:
            return self.fetch_feature(key, **kwargs)
        return self.fetch_symbol(key, **kwargs)

    def update_feature(self, feature: tp.Feature, **kwargs) -> tp.FeatureData:
        """Update data of a feature.

        Args:
            feature (Feature): The feature key to update.
            **kwargs: Keyword arguments passed to `CSVData.update_key`.

        Returns:
            FeatureData: The updated data and a metadata dictionary.

        !!! note
            Invokes `CSVData.update_key` with `key_is_feature=True`.
        """
        return self.update_key(feature, key_is_feature=True, **kwargs)

    def update_symbol(self, symbol: tp.Symbol, **kwargs) -> tp.SymbolData:
        """Update data for a symbol.

        Invokes `CSVData.update_key` with `key_is_feature=False`.

        Args:
            symbol (Symbol): The symbol key whose data will be updated.
            **kwargs: Keyword arguments passed to `CSVData.update_key`.

        Returns:
            SymbolData: The updated data and a metadata dictionary.
        """
        return self.update_key(symbol, key_is_feature=False, **kwargs)
