# ==================================== VBTPROXYZ ====================================
# Copyright (c) 2021-2025 Oleg Polakow. All rights reserved.
#
# This file is part of the proprietary VectorBT® PRO package and is licensed under
# the VectorBT® PRO License available at https://vectorbt.pro/terms/software-license/
#
# Unauthorized publishing, distribution, sublicensing, or sale of this software
# or its parts is strictly prohibited.
# ===================================================================================

"""Module providing base classes and functions for resampling."""

import numpy as np
import pandas as pd

from vectorbtpro import _typing as tp
from vectorbtpro.base.indexes import repeat_index
from vectorbtpro.base.resampling import nb
from vectorbtpro.registries.jit_registry import jit_reg
from vectorbtpro.utils import checks, datetime_ as dt
from vectorbtpro.utils.config import Configured
from vectorbtpro.utils.decorators import cached_property, hybrid_method
from vectorbtpro.utils.warnings_ import warn

__all__ = [
    "Resampler",
]

ResamplerT = tp.TypeVar("ResamplerT", bound="Resampler")


class Resampler(Configured):
    """Class for resampling an index.

    Args:
        source_index (index_like): Source index to be resampled.
        target_index (index_like): Target index produced by resampling.
        source_freq (frequency_like or bool): Source index frequency or date offset.

            Set to False to disable automatic frequency inference.
        target_freq (frequency_like or bool): Target index frequency or date offset.

            Set to False to disable automatic frequency inference.
        silence_warnings (bool): Flag indicating whether warnings should be silenced.
    """

    def __init__(
        self,
        source_index: tp.IndexLike,
        target_index: tp.IndexLike,
        source_freq: tp.Union[None, bool, tp.FrequencyLike] = None,
        target_freq: tp.Union[None, bool, tp.FrequencyLike] = None,
        silence_warnings: tp.Optional[bool] = None,
        **kwargs,
    ) -> None:
        source_index = dt.prepare_dt_index(source_index)
        target_index = dt.prepare_dt_index(target_index)
        infer_source_freq = True
        if isinstance(source_freq, bool):
            if not source_freq:
                infer_source_freq = False
            source_freq = None
        infer_target_freq = True
        if isinstance(target_freq, bool):
            if not target_freq:
                infer_target_freq = False
            target_freq = None
        if infer_source_freq:
            source_freq = dt.infer_index_freq(source_index, freq=source_freq)
        if infer_target_freq:
            target_freq = dt.infer_index_freq(target_index, freq=target_freq)

        self._source_index = source_index
        self._target_index = target_index
        self._source_freq = source_freq
        self._target_freq = target_freq
        self._silence_warnings = silence_warnings

        Configured.__init__(
            self,
            source_index=source_index,
            target_index=target_index,
            source_freq=source_freq,
            target_freq=target_freq,
            silence_warnings=silence_warnings,
            **kwargs,
        )

    @property
    def source_index(self) -> tp.Index:
        """Source index used for resampling."""
        return self._source_index

    @property
    def target_index(self) -> tp.Index:
        """Target index produced by resampling."""
        return self._target_index

    @property
    def source_freq(self) -> tp.AnyPandasFrequency:
        """Source index frequency or date offset."""
        return self._source_freq

    @property
    def target_freq(self) -> tp.AnyPandasFrequency:
        """Target index frequency or date offset."""
        return self._target_freq

    @property
    def silence_warnings(self) -> bool:
        """Flag indicating whether warnings are silenced."""
        from vectorbtpro._settings import settings

        resampling_cfg = settings["resampling"]

        silence_warnings = self._silence_warnings
        if silence_warnings is None:
            silence_warnings = resampling_cfg["silence_warnings"]
        return silence_warnings

    @classmethod
    def from_pd_resampler(
        cls: tp.Type[ResamplerT],
        pd_resampler: tp.PandasResampler,
        source_freq: tp.Optional[tp.FrequencyLike] = None,
        silence_warnings: bool = True,
    ) -> ResamplerT:
        """Create a `Resampler` instance from a `pandas.core.resample.Resampler` object.

        Args:
            pd_resampler (pandas.core.resample.Resampler): A pandas resampler object.
            source_freq (Optional[FrequencyLike]): Frequency for the source index.
            silence_warnings (bool): Flag indicating whether warnings should be silenced.

        Returns:
            Resampler: A new `Resampler` instance.
        """
        target_index = pd_resampler.count().index
        return cls(
            source_index=pd_resampler.obj.index,
            target_index=target_index,
            source_freq=source_freq,
            target_freq=None,
            silence_warnings=silence_warnings,
        )

    @classmethod
    def from_pd_resample(
        cls: tp.Type[ResamplerT],
        source_index: tp.IndexLike,
        *args,
        source_freq: tp.Optional[tp.FrequencyLike] = None,
        silence_warnings: bool = True,
        **kwargs,
    ) -> ResamplerT:
        """Create a `Resampler` instance using the `pandas.Series.resample` method.

        Args:
            source_index (index_like): Source index for resampling.
            *args: Positional arguments passed to `pandas.Series.resample`.
            source_freq (Optional[FrequencyLike]): Frequency for the source index.
            silence_warnings (bool): Flag indicating whether warnings should be silenced.
            **kwargs: Keyword arguments passed to `pandas.Series.resample`.

        Returns:
            Resampler: A new `Resampler` instance.
        """
        pd_resampler = pd.Series(index=source_index, dtype=object).resample(*args, **kwargs)
        return cls.from_pd_resampler(pd_resampler, source_freq=source_freq, silence_warnings=silence_warnings)

    @classmethod
    def from_date_range(
        cls: tp.Type[ResamplerT],
        source_index: tp.IndexLike,
        *args,
        source_freq: tp.Optional[tp.FrequencyLike] = None,
        silence_warnings: tp.Optional[bool] = None,
        **kwargs,
    ) -> ResamplerT:
        """Create a `Resampler` instance using `vectorbtpro.utils.datetime_.date_range`.

        Args:
            source_index (index_like): Source index for resampling.
            *args: Positional arguments passed to `vectorbtpro.utils.datetime_.date_range`.
            source_freq (Optional[FrequencyLike]): Frequency for the source index.
            silence_warnings (Optional[bool]): Flag indicating whether warnings should be silenced.
            **kwargs: Keyword arguments passed to `vectorbtpro.utils.datetime_.date_range`.

        Returns:
            Resampler: A new `Resampler` instance.
        """
        target_index = dt.date_range(*args, **kwargs)
        return cls(
            source_index=source_index,
            target_index=target_index,
            source_freq=source_freq,
            target_freq=None,
            silence_warnings=silence_warnings,
        )

    def get_np_source_freq(self, silence_warnings: tp.Optional[bool] = None) -> tp.AnyPandasFrequency:
        """Convert the source index frequency to NumPy format.

        Args:
            silence_warnings (Optional[bool]): Flag indicating whether warnings should be silenced.

        Returns:
            AnyPandasFrequency: Source index frequency in NumPy format.
        """
        if silence_warnings is None:
            silence_warnings = self.silence_warnings

        warned = False
        source_freq = self.source_freq
        if source_freq is not None:
            if not isinstance(source_freq, (int, float)):
                try:
                    source_freq = dt.to_timedelta64(source_freq)
                except ValueError as e:
                    if not silence_warnings:
                        warn(f"Cannot convert {source_freq} to np.timedelta64. Setting to None.")
                        warned = True
                    source_freq = None
        if source_freq is None:
            if not warned and not silence_warnings:
                warn("Using right bound of source index without frequency. Set source frequency.")
        return source_freq

    def get_np_target_freq(self, silence_warnings: tp.Optional[bool] = None) -> tp.AnyPandasFrequency:
        """Convert the target index frequency to NumPy format.

        Args:
            silence_warnings (Optional[bool]): Flag indicating whether warnings should be silenced.

        Returns:
            AnyPandasFrequency: Target index frequency in NumPy format.
        """
        if silence_warnings is None:
            silence_warnings = self.silence_warnings

        warned = False
        target_freq = self.target_freq
        if target_freq is not None:
            if not isinstance(target_freq, (int, float)):
                try:
                    target_freq = dt.to_timedelta64(target_freq)
                except ValueError as e:
                    if not silence_warnings:
                        warn(f"Cannot convert {target_freq} to np.timedelta64. Setting to None.")
                        warned = True
                    target_freq = None
        if target_freq is None:
            if not warned and not silence_warnings:
                warn("Using right bound of target index without frequency. Set target frequency.")
        return target_freq

    @classmethod
    def get_lbound_index(cls, index: tp.Index, freq: tp.AnyPandasFrequency = None) -> tp.Index:
        """Calculate the left bound of a datetime index.

        Args:
            index (Index): Datetime index for which to calculate the left bound.
            freq (AnyPandasFrequency): Frequency for adjusting the index.

        Returns:
            Index: Datetime index representing the calculated left bound.
        """
        index = dt.prepare_dt_index(index)
        checks.assert_instance_of(index, pd.DatetimeIndex)
        if freq is not None:
            return index.shift(-1, freq=freq) + pd.Timedelta(1, "ns")
        min_ts = pd.DatetimeIndex([pd.Timestamp.min.tz_localize(index.tz)])
        return (index[:-1] + pd.Timedelta(1, "ns")).append(min_ts)

    @classmethod
    def get_rbound_index(cls, index: tp.Index, freq: tp.AnyPandasFrequency = None) -> tp.Index:
        """Return the right bound of a datetime index.

        Args:
            index (Index): A datetime index.
            freq (AnyPandasFrequency): A frequency used to shift the index.

                If None, the rightmost bound is calculated.

        Returns:
            Index: A datetime index representing the computed right bound.
        """
        index = dt.prepare_dt_index(index)
        checks.assert_instance_of(index, pd.DatetimeIndex)
        if freq is not None:
            return index.shift(1, freq=freq) - pd.Timedelta(1, "ns")
        max_ts = pd.DatetimeIndex([pd.Timestamp.max.tz_localize(index.tz)])
        return (index[1:] - pd.Timedelta(1, "ns")).append(max_ts)

    @cached_property
    def source_lbound_index(self) -> tp.Index:
        """Left bound of the source datetime index."""
        return self.get_lbound_index(self.source_index, freq=self.source_freq)

    @cached_property
    def source_rbound_index(self) -> tp.Index:
        """Right bound of the source datetime index."""
        return self.get_rbound_index(self.source_index, freq=self.source_freq)

    @cached_property
    def target_lbound_index(self) -> tp.Index:
        """Left bound of the target datetime index."""
        return self.get_lbound_index(self.target_index, freq=self.target_freq)

    @cached_property
    def target_rbound_index(self) -> tp.Index:
        """Right bound of the target datetime index."""
        return self.get_rbound_index(self.target_index, freq=self.target_freq)

    def map_to_target_index(
        self,
        before: bool = False,
        raise_missing: bool = True,
        return_index: bool = True,
        jitted: tp.JittedOption = None,
        silence_warnings: tp.Optional[bool] = None,
    ) -> tp.Union[tp.Array1d, tp.Index]:
        """Return the mapping from the source index to the target index.

        See `vectorbtpro.base.resampling.nb.map_to_target_index_nb`.

        Args:
            before (bool): Map to the target index before the given value if True.
            raise_missing (bool): Raise an error if a target index value is missing.
            return_index (bool): Return a pandas Index if True; otherwise, return a numpy array.
            jitted (JittedOption): Option to control JIT compilation.
            silence_warnings (Optional[bool]): Whether to silence warnings.

        Returns:
            Union[Array1d, Index]: The mapped index values.
        """
        target_freq = self.get_np_target_freq(silence_warnings=silence_warnings)
        func = jit_reg.resolve_option(nb.map_to_target_index_nb, jitted)
        mapped_arr = func(
            self.source_index.values,
            self.target_index.values,
            target_freq=target_freq,
            before=before,
            raise_missing=raise_missing,
        )
        if return_index:
            nan_mask = mapped_arr == -1
            if nan_mask.any():
                mapped_index = self.source_index.to_series().copy()
                mapped_index[nan_mask] = np.nan
                mapped_index[~nan_mask] = self.target_index[mapped_arr]
                mapped_index = pd.Index(mapped_index)
            else:
                mapped_index = self.target_index[mapped_arr]
            return mapped_index
        return mapped_arr

    def index_difference(
        self,
        reverse: bool = False,
        return_index: bool = True,
        jitted: tp.JittedOption = None,
    ) -> tp.Union[tp.Array1d, tp.Index]:
        """Return the index difference mapping between the source and target indices.

        See `vectorbtpro.base.resampling.nb.index_difference_nb`.

        Args:
            reverse (bool): Reverse the order of indices for difference calculation if True.
            return_index (bool): Return the target index mapping if True; otherwise, return a numpy array.
            jitted (JittedOption): Option to control JIT compilation.

        Returns:
            Union[Array1d, Index]: The computed index difference mapping.
        """
        func = jit_reg.resolve_option(nb.index_difference_nb, jitted)
        if reverse:
            mapped_arr = func(self.target_index.values, self.source_index.values)
        else:
            mapped_arr = func(self.source_index.values, self.target_index.values)
        if return_index:
            return self.target_index[mapped_arr]
        return mapped_arr

    def map_index_to_source_ranges(
        self,
        before: bool = False,
        jitted: tp.JittedOption = None,
        silence_warnings: tp.Optional[bool] = None,
    ) -> tp.Tuple[tp.Array1d, tp.Array1d]:
        """Return the mapping of source index ranges corresponding to the target index.

        See `vectorbtpro.base.resampling.nb.map_index_to_source_ranges_nb`.

        Args:
            before (bool): Map to source ranges before the target index if True.
            jitted (JittedOption): Option to control JIT compilation.
            silence_warnings (Optional[bool]): Whether to silence warnings.

        Returns:
            Tuple[Array1d, Array1d]: A tuple with the start and end indices of the source ranges.

        !!! note
            If `Resampler.target_freq` is a date offset, it is set to None and a warning is emitted.
            An additional warning is raised if `target_freq` is None.
        """
        target_freq = self.get_np_target_freq(silence_warnings=silence_warnings)
        func = jit_reg.resolve_option(nb.map_index_to_source_ranges_nb, jitted)
        return func(
            self.source_index.values,
            self.target_index.values,
            target_freq=target_freq,
            before=before,
        )

    @hybrid_method
    def map_bounds_to_source_ranges(
        cls_or_self,
        source_index: tp.Optional[tp.IndexLike] = None,
        target_lbound_index: tp.Optional[tp.IndexLike] = None,
        target_rbound_index: tp.Optional[tp.IndexLike] = None,
        closed_lbound: bool = True,
        closed_rbound: bool = False,
        skip_not_found: bool = False,
        jitted: tp.JittedOption = None,
    ) -> tp.Tuple[tp.Array1d, tp.Array1d]:
        """Return the mapping from target index bounds to source index ranges.

        Either `target_lbound_index` or `target_rbound_index` must be provided.

        See `vectorbtpro.base.resampling.nb.map_bounds_to_source_ranges_nb`.

        Args:
            source_index (Optional[IndexLike]): A source datetime index.
            target_lbound_index (Optional[IndexLike]): The left bound of the target index.

                Set to "pandas" to use `Resampler.get_lbound_index`.
            target_rbound_index (Optional[IndexLike]): The right bound of the target index.

                Set to "pandas" to use `Resampler.get_rbound_index`.
            closed_lbound (bool): Specify whether the left bound is closed.
            closed_rbound (bool): Specify whether the right bound is closed.
            skip_not_found (bool): If True, skip mapping for indices that are not found.
            jitted (JittedOption): Option to control JIT compilation.

        Returns:
            Tuple[Array1d, Array1d]: A pair of arrays representing the mapping from target bounds to source ranges.
        """
        if not isinstance(cls_or_self, type):
            if target_lbound_index is None and target_rbound_index is None:
                raise ValueError("Either target_lbound_index or target_rbound_index must be set")
            if target_lbound_index is not None:
                if isinstance(target_lbound_index, str) and target_lbound_index.lower() == "pandas":
                    target_lbound_index = cls_or_self.target_lbound_index
                else:
                    target_lbound_index = dt.prepare_dt_index(target_lbound_index)
                target_rbound_index = cls_or_self.target_index
            if target_rbound_index is not None:
                target_lbound_index = cls_or_self.target_index
                if isinstance(target_rbound_index, str) and target_rbound_index.lower() == "pandas":
                    target_rbound_index = cls_or_self.target_rbound_index
                else:
                    target_rbound_index = dt.prepare_dt_index(target_rbound_index)
            if len(target_lbound_index) == 1 and len(target_rbound_index) > 1:
                target_lbound_index = repeat_index(target_lbound_index, len(target_rbound_index))
            elif len(target_lbound_index) > 1 and len(target_rbound_index) == 1:
                target_rbound_index = repeat_index(target_rbound_index, len(target_lbound_index))
        else:
            source_index = dt.prepare_dt_index(source_index)
            target_lbound_index = dt.prepare_dt_index(target_lbound_index)
            target_rbound_index = dt.prepare_dt_index(target_rbound_index)

        checks.assert_len_equal(target_rbound_index, target_lbound_index)
        func = jit_reg.resolve_option(nb.map_bounds_to_source_ranges_nb, jitted)
        return func(
            source_index.values,
            target_lbound_index.values,
            target_rbound_index.values,
            closed_lbound=closed_lbound,
            closed_rbound=closed_rbound,
            skip_not_found=skip_not_found,
        )

    def resample_source_mask(
        self,
        source_mask: tp.ArrayLike,
        jitted: tp.JittedOption = None,
        silence_warnings: tp.Optional[bool] = None,
    ) -> tp.Array1d:
        """Return a resampled mask for the source index.

        See `vectorbtpro.base.resampling.nb.resample_source_mask_nb`.

        Args:
            source_mask (ArrayLike): A boolean mask for the source index.
            jitted (JittedOption): Option to control JIT compilation.
            silence_warnings (Optional[bool]): Whether to silence warnings.

        Returns:
            Array1d: A resampled array corresponding to the source mask.
        """
        from vectorbtpro.base.reshaping import broadcast_array_to

        if silence_warnings is None:
            silence_warnings = self.silence_warnings
        source_mask = broadcast_array_to(source_mask, len(self.source_index))
        source_freq = self.get_np_source_freq(silence_warnings=silence_warnings)
        target_freq = self.get_np_target_freq(silence_warnings=silence_warnings)

        func = jit_reg.resolve_option(nb.resample_source_mask_nb, jitted)
        return func(
            source_mask,
            self.source_index.values,
            self.target_index.values,
            source_freq,
            target_freq,
        )

    def last_before_target_index(self, incl_source: bool = True, jitted: tp.JittedOption = None) -> tp.Array1d:
        """Return the index of the last element before each target index.

        See `vectorbtpro.base.resampling.nb.last_before_target_index_nb`.

        Args:
            incl_source (bool): Include the source index in the computation if True.
            jitted (JittedOption): Option to control JIT compilation.

        Returns:
            Array1d: An array of indices representing the last element before each target index.
        """
        func = jit_reg.resolve_option(nb.last_before_target_index_nb, jitted)
        return func(self.source_index.values, self.target_index.values, incl_source=incl_source)
