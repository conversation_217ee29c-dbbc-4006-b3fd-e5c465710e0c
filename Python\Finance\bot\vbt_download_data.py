# fmt: off
import os
import sys
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
import utils
# fmt: on

from aligo import Aligo
import datetime as dt
import logging
import math
import subprocess
import threading
import time
from concurrent.futures import thread
import pandas as pd
import pandas_ta as ta
import strategies
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
import numpy as np
import pandas as pd
from numba import njit
import vectorbtpro as vbt
from collections import namedtuple
import pandas as pd
import pendulum
import vbt_custom_simulation


def handle():
    symbol = "DOGEUSDT"
    start_date = "2022-01-01"
    end_date = "2025-06-30"
    time_frame = 1  # 1m, 3m, 5m

    # Convert end_date to pendulum date and get next day
    end_date_dt = pendulum.parse(end_date)
    next_day = end_date_dt.add(days=1).to_date_string()

    # Use next_day as end parameter while keeping end_date for filename
    df = vbt.BinanceData.pull(symbol, start=start_date, end=next_day, timeframe=f"{time_frame}m").get()
    df.index = df.index.tz_convert(None)
    df = df.reset_index()
    columns_to_keep = ["Open time", "Open", "High", "Low", "Close", "Volume"]
    df = df[columns_to_keep]
    df.rename(columns={"Open time": "OpenTime"}, inplace=True)
    df.set_index("OpenTime", inplace=True)
    df.to_csv(f"D:/diagnose/{symbol}_Candlestick_({time_frame}m)_({start_date}-{end_date}).csv", index=True)


def main():
    if sys.platform == "win32":
        os.system("cls")
    else:
        os.system("clear")

    t0 = pendulum.now()

    utils.init_logging()

    handle()

    logging.info("-" * 60)
    t1 = pendulum.now()
    delta = t1 - t0
    logging.info(f"elapsed time: `{delta.in_words(locale='en')}`")

    print("about to exit~")


if __name__ == "__main__":
    main()
