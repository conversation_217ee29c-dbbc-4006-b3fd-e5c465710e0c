# ==================================== VBTPROXYZ ====================================
# Copyright (c) 2021-2025 Oleg Polakow. All rights reserved.
#
# This file is part of the proprietary VectorBT® PRO package and is licensed under
# the VectorBT® PRO License available at https://vectorbt.pro/terms/software-license/
#
# Unauthorized publishing, distribution, sublicensing, or sale of this software
# or its parts is strictly prohibited.
# ===================================================================================

"""Default data types for internal use."""

from vectorbtpro._settings import settings

int_ = settings["numpy"]["int_"]
"""Default integer data type."""

float_ = settings["numpy"]["float_"]
"""Default floating data type."""

__all__ = [
    "int_",
    "float_",
]
