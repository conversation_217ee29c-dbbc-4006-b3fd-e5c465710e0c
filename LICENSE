VECTORBT® PRO LICENSE

Last Updated: January 5, 2025

This Software License (“License”) is a legal agreement between the Copyright
Holder and the Authorized User, governing the use of VECTORBT® PRO. By
downloading, installing, or using the Software, you agree to be bound by this
License.

1. DEFINITIONS

1.1 “Software”
Refers to VECTORBT® PRO, including all related documentation, updates, and
files provided under this License.

1.2 “Copyright Holder”
Refers to <PERSON><PERSON>, along with any successors, assigns, or authorized legal
representatives.

1.3 “Authorized User”
Means:
  (a) Individual: A single person who obtains the Software for personal,
      private, and non-commercial use (“Private Use”).
  (b) Organization: A legal entity that acquires the Software solely for
      internal, non-commercial purposes. Use is restricted to employees or
      direct contractors who are explicitly granted access to the Software as
      part of their duties for internal purposes.

1.4 “Private Use” / “Non-Commercial Use”
Refers to any use of the Software where no direct or indirect monetary
compensation, commercial advantage, or for-profit purpose is obtained. This
includes:
  i.  Individual Users: Personal, private use with no commercial intent.
  ii. Organizations (including for-profit entities): Internal, in-house usage
      for purposes such as evaluation, data analysis, prototyping, or research
      and development (R&D), provided it does not result in a commercial
      product, service, or outcome. Any use that contributes to or is
      integrated into a commercial offering requires a separate commercial
      license.

1.5 “Sell”
Means to use, reproduce, distribute, host, monetize, or otherwise commercialize
the Software—directly or indirectly—in exchange for payment or other
compensation. This includes offering services that derive significant value
from the Software’s core functionality.

2. LICENSE GRANT & SCOPE

2.1 License Grant
The Copyright Holder grants the Authorized User a limited, revocable,
non-exclusive, and non-transferable license to download, install, use, copy,
and modify the Software exclusively for Private Use / Non-Commercial Use, as
defined in Section 1.4.

2.2 Scope of Authorized Use
  (a) Individual Users: May use the Software on their personal devices solely
      for private, non-commercial purposes.
  (b) Organizations: May use the Software internally for non-commercial
      purposes. The Organization must ensure that only authorized employees or
      contractors access or interact with the Software. The Organization is
      responsible for preventing:
      i.  Unauthorized internal sharing with unapproved employees or
          contractors.
      ii. Any external access by third parties unless explicitly permitted in
          writing by the Copyright Holder.

2.3 Modifications & Internal Sharing
  i.   Authorized Users may create modifications or derivative works for
       Private Use / Non-Commercial Use only.
  ii.  Modifications or derivative works may be shared among Authorized Users,
       provided they remain inaccessible to any party not covered by this
       License.
  iii. Modifications or improvements may be shared directly with the Copyright
       Holder without violating this License.

2.4 Updates & Versions
  i.  All updates, patches, or new versions of the Software provided by the
      Copyright Holder are subject to this License unless otherwise specified.
  ii. The Copyright Holder reserves the right to modify the terms of this
      License for future versions. Continued use of any updated version
      indicates acceptance of the revised terms.

Note: Any use outside the scope defined above—including but not limited to
commercial use—requires a separate written agreement with the Copyright Holder.

3. RESTRICTIONS & LIMITATIONS

3.1 No Unauthorized Distribution or Sharing
The Software must not be published, shared, uploaded to publicly accessible
platforms, distributed, transmitted, or made available to any third party
without prior written consent from the Copyright Holder.

Exception: Internal hosting within an Organization’s private, secure network is
permitted, provided it remains inaccessible to external parties or unauthorized
personnel.

3.2 No Sublicensing or Commercial Use
The Authorized User may not sublicense, Sell, or otherwise use the Software for
commercial purposes.

Clarification: “Sell” includes any form of monetization derived from the
Software’s core functionality, such as hosting, consulting, or support services
offered for a fee.

3.3 Retention of Copyright Notices
All copies, modifications, or derivative works of the Software must retain the
original copyright notice and this License. The removal or alteration of any
proprietary notices is prohibited.

3.4 No Reverse Engineering
The Authorized User may not decompile, decrypt, reverse engineer, disassemble,
or otherwise attempt to derive the source code of the Software, except where
permitted by applicable law.

3.5 Third-Party Components
If the Software includes third-party components covered by separate licenses,
those components are subject to their respective terms. This License does not
override any third-party agreements.

4. ADDITIONAL RIGHTS REQUIRE WRITTEN CONSENT

Any rights to distribute, sublicense, Sell, or otherwise commercially exploit
the Software—whether modified or unmodified—must be explicitly granted through
a separate written agreement with the Copyright Holder.

No additional rights are implied or granted beyond those expressly stated in
this License.

5. TERMINATION & RESERVATION OF RIGHTS

5.1 Termination
This License automatically terminates if the Authorized User breaches any of
its terms. Upon termination, the Authorized User must immediately cease all use
of the Software and permanently delete all copies, including backups.

5.2 Reservation of Rights
All rights not expressly granted under this License are reserved by the
Copyright Holder.

6. DISCLAIMER & INDEMNIFICATION

6.1 Disclaimer of Warranty
THE SOFTWARE IS PROVIDED “AS IS,” WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT. THE COPYRIGHT HOLDER SHALL NOT
BE LIABLE FOR ANY CLAIMS, DAMAGES, OR LIABILITIES ARISING FROM THE USE OR
INABILITY TO USE THE SOFTWARE, EXCEPT AS REQUIRED BY APPLICABLE LAW.

6.2 Indemnification
The Authorized User agrees to indemnify, defend, and hold harmless the
Copyright Holder from any claims, damages, liabilities, costs, or expenses
(including reasonable attorneys’ fees) arising from the Authorized User’s use
or misuse of the Software.

7. SEVERABILITY

If any provision of this License is found by a court of competent jurisdiction
to be invalid, illegal, or unenforceable, that provision shall be deemed
modified to the minimum extent necessary to render it valid and enforceable.
If modification is not possible, the provision shall be severed, and the
remaining provisions shall continue in full force and effect.

8. TRADEMARK USAGE

8.1 Ownership
“VECTORBT®” is a registered trademark owned by the Copyright Holder.

8.2 Limited Reference Rights
This License permits limited use of the VECTORBT® trademark to refer to the
Software in documentation or promotional materials, provided it does not
suggest endorsement by the Copyright Holder.

8.3 Prohibited Uses
You may not use, adopt, or register any trademark or domain name that is
identical or confusingly similar to VECTORBT®.

8.4 No Endorsement
Use of the VECTORBT® trademark may not imply endorsement or affiliation without
explicit written authorization.

[End of License]
