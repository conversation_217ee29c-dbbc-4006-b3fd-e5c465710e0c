import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import re
import os
from datetime import datetime
import numpy as np


def add_auto_rangebreaks(fig, bar_indices=None):
    """
    Add automatic range breaks functionality similar to vectorbt's auto_rangebreaks.
    This enables better zooming and navigation for time series data.
    """
    if bar_indices is None:
        return fig

    # Calculate gaps in the data that might need range breaks
    if len(bar_indices) > 1:
        gaps = np.diff(bar_indices)
        median_gap = np.median(gaps)
        large_gaps = np.where(gaps > median_gap * 3)[0]  # Find gaps 3x larger than median

        # Create range breaks for large gaps
        rangebreaks = []
        for gap_idx in large_gaps:
            start_break = bar_indices[gap_idx] + median_gap
            end_break = bar_indices[gap_idx + 1] - median_gap
            if end_break > start_break:
                rangebreaks.append(dict(bounds=[start_break, end_break]))

        # Apply range breaks to all x-axes
        if rangebreaks:
            fig.update_xaxes(rangebreaks=rangebreaks)

    return fig


# Define the log file path
log_file = r"D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\NebulaVilla-log.txt"

# Lists to store extracted data
bar_indices = []
fdusd_values = []
ohlc_data = []
signals = []
trade_events = []

# Regular expressions
fdusd_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) barIdx=(\d+), opening=0, position=0, pendingOrders=0, FDUSD=(\d+\.\d{2})"
vbt_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) barIdx=(\d+), .*, vbt: `(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}), ([\d.]+), ([\d.]+), ([\d.]+), ([\d.]+), (\d+), ([012])`"
trade_pattern = r"barIdx=(\d+), .*, TimeDiff\(Filled\)=([\d.]+)s, QTY=([+-]?\d+), .*, fillPrice=([\d.]+), property=(\w+)"

# Read and process the log file
with open(log_file, "r") as file:
    for line in file:
        # Extract FDUSD data
        fdusd_match = re.search(fdusd_pattern, line.strip())
        if fdusd_match:
            bar_idx = int(fdusd_match.group(2))
            fdusd = float(fdusd_match.group(3))
            bar_indices.append(bar_idx)
            fdusd_values.append(fdusd)

        # Extract OHLC and signal data from vbt lines
        vbt_match = re.search(vbt_pattern, line.strip())
        if vbt_match:
            timestamp = datetime.strptime(vbt_match.group(1), "%Y-%m-%d %H:%M:%S")
            bar_idx = int(vbt_match.group(2))
            vbt_timestamp = datetime.strptime(vbt_match.group(3), "%Y-%m-%d %H:%M:%S")
            open_price = float(vbt_match.group(4))
            high_price = float(vbt_match.group(5))
            low_price = float(vbt_match.group(6))
            close_price = float(vbt_match.group(7))
            volume = int(vbt_match.group(8))
            signal = int(vbt_match.group(9))

            ohlc_data.append({"timestamp": timestamp, "bar_idx": bar_idx, "vbt_timestamp": vbt_timestamp, "open": open_price, "high": high_price, "low": low_price, "close": close_price, "volume": volume, "signal": signal})

        # Extract trade execution events
        trade_match = re.search(trade_pattern, line.strip())
        if trade_match:
            bar_idx = int(trade_match.group(1))
            time_diff = float(trade_match.group(2))
            qty = int(trade_match.group(3))
            fill_price = float(trade_match.group(4))
            property_type = trade_match.group(5)

            trade_events.append({"bar_idx": bar_idx, "time_diff": time_diff, "qty": qty, "fill_price": fill_price, "property": property_type, "is_buy": qty > 0, "is_open": "open" in property_type.lower()})

# Convert to DataFrames for easier handling
if ohlc_data:
    ohlc_df = pd.DataFrame(ohlc_data)

if trade_events:
    trades_df = pd.DataFrame(trade_events)

# Create comprehensive plot with Plotly subplots
fig = make_subplots(rows=3, cols=1, shared_xaxes=True, vertical_spacing=0.05, subplot_titles=("OHLC Candlestick Chart with Buy/Sell Signals", "Trade Execution Events", "FDUSD Value Changes Over Time"), row_heights=[0.5, 0.25, 0.25])

# Plot 1: OHLC Candlestick Chart
if ohlc_data:
    # Create candlestick data
    candlestick_trace = go.Candlestick(x=ohlc_df["bar_idx"], open=ohlc_df["open"], high=ohlc_df["high"], low=ohlc_df["low"], close=ohlc_df["close"], name="", increasing_line_color="green", decreasing_line_color="red", showlegend=True)
    fig.add_trace(candlestick_trace, row=1, col=1)

    # Add buy/sell signals
    short_signals = ohlc_df[ohlc_df["signal"] == 1]
    long_signals = ohlc_df[ohlc_df["signal"] == 2]

    if not short_signals.empty:
        fig.add_trace(go.Scatter(x=short_signals["bar_idx"], y=short_signals["high"] * 1.01, mode="markers", marker=dict(symbol="triangle-down", color="red", size=10), name="Short Signal", showlegend=True), row=1, col=1)

    if not long_signals.empty:
        fig.add_trace(go.Scatter(x=long_signals["bar_idx"], y=long_signals["low"] * 0.99, mode="markers", marker=dict(symbol="triangle-up", color="green", size=10), name="Long Signal", showlegend=True), row=1, col=1)

# Plot 2: Trade Events - Separate Open and Close Events
if trade_events:
    # Separate events by open/close first, then by buy/sell
    open_events = [event for event in trade_events if event["is_open"]]
    close_events = [event for event in trade_events if not event["is_open"]]

    # Further separate by buy/sell within each category
    open_buy_events = [event for event in open_events if event["is_buy"]]
    open_sell_events = [event for event in open_events if not event["is_buy"]]
    close_buy_events = [event for event in close_events if event["is_buy"]]
    close_sell_events = [event for event in close_events if not event["is_buy"]]

    # Display Opening Positions (Buy)
    if open_buy_events:
        open_buy_bar_indices = [event["bar_idx"] for event in open_buy_events]
        open_buy_prices = [event["fill_price"] for event in open_buy_events]
        open_buy_hover_texts = []
        for event in open_buy_events:
            hover_text = f"<b>开仓 (Buy Open)</b><br>Bar Index: {event['bar_idx']}<br>Fill Price: {event['fill_price']}<br>开仓QTY: +{event['qty']} 🔓<br>Property: {event['property']}"
            open_buy_hover_texts.append(hover_text)

        fig.add_trace(
            go.Scatter(
                x=open_buy_bar_indices,
                y=open_buy_prices,
                mode="markers",
                marker=dict(symbol="triangle-up", color="green", size=8),
                name="开仓买入",
                showlegend=True,
                hovertemplate="%{text}<extra></extra>",
                text=open_buy_hover_texts,
            ),
            row=2,
            col=1,
        )

    # Display Opening Positions (Sell)
    if open_sell_events:
        open_sell_bar_indices = [event["bar_idx"] for event in open_sell_events]
        open_sell_prices = [event["fill_price"] for event in open_sell_events]
        open_sell_hover_texts = []
        for event in open_sell_events:
            hover_text = f"<b>开仓 (Sell Open)</b><br>Bar Index: {event['bar_idx']}<br>Fill Price: {event['fill_price']}<br>开仓QTY: {event['qty']} 🔓<br>Property: {event['property']}"
            open_sell_hover_texts.append(hover_text)

        fig.add_trace(
            go.Scatter(
                x=open_sell_bar_indices,
                y=open_sell_prices,
                mode="markers",
                marker=dict(symbol="triangle-down", color="red", size=8),
                name="开仓卖出",
                showlegend=True,
                hovertemplate="%{text}<extra></extra>",
                text=open_sell_hover_texts,
            ),
            row=2,
            col=1,
        )

    # Display Closing Positions (Buy)
    if close_buy_events:
        close_buy_bar_indices = [event["bar_idx"] for event in close_buy_events]
        close_buy_prices = [event["fill_price"] for event in close_buy_events]
        close_buy_hover_texts = []
        for event in close_buy_events:
            property_lower = event["property"].lower()
            if "close" in property_lower and ("full" in property_lower or "complete" in property_lower):
                hover_text = f"<b>完全平仓 (Full Close)</b><br>Bar Index: {event['bar_idx']}<br>Fill Price: {event['fill_price']}<br>QTY: +{event['qty']} 🔒<br>Property: {event['property']}"
            else:
                hover_text = f"<b>平仓 (Buy Close)</b><br>Bar Index: {event['bar_idx']}<br>Fill Price: {event['fill_price']}<br>平仓QTY: +{event['qty']}<br>Property: {event['property']}"
            close_buy_hover_texts.append(hover_text)

        fig.add_trace(
            go.Scatter(
                x=close_buy_bar_indices,
                y=close_buy_prices,
                mode="markers",
                marker=dict(symbol="triangle-up", color="lightgreen", size=8),
                name="平仓买入",
                showlegend=True,
                hovertemplate="%{text}<extra></extra>",
                text=close_buy_hover_texts,
            ),
            row=2,
            col=1,
        )

    # Display Closing Positions (Sell)
    if close_sell_events:
        close_sell_bar_indices = [event["bar_idx"] for event in close_sell_events]
        close_sell_prices = [event["fill_price"] for event in close_sell_events]
        close_sell_hover_texts = []
        for event in close_sell_events:
            property_lower = event["property"].lower()
            if "close" in property_lower and ("full" in property_lower or "complete" in property_lower):
                hover_text = f"<b>完全平仓 (Full Close)</b><br>Bar Index: {event['bar_idx']}<br>Fill Price: {event['fill_price']}<br>QTY: {event['qty']} 🔒<br>Property: {event['property']}"
            else:
                hover_text = f"<b>平仓 (Sell Close)</b><br>Bar Index: {event['bar_idx']}<br>Fill Price: {event['fill_price']}<br>平仓QTY: {event['qty']}<br>Property: {event['property']}"
            close_sell_hover_texts.append(hover_text)

        fig.add_trace(
            go.Scatter(
                x=close_sell_bar_indices,
                y=close_sell_prices,
                mode="markers",
                marker=dict(symbol="triangle-down", color="lightcoral", size=8),
                name="平仓卖出",
                showlegend=True,
                hovertemplate="%{text}<extra></extra>",
                text=close_sell_hover_texts,
            ),
            row=2,
            col=1,
        )


# Plot 3: FDUSD Values
if bar_indices:
    fig.add_trace(go.Scatter(x=bar_indices, y=fdusd_values, mode="lines", line=dict(color="blue", width=2), name="FDUSD Value", showlegend=True), row=3, col=1)

# Update layout for better appearance and interactivity
fig.update_layout(
    height=800,
    showlegend=True,
    title_text="Trading Analysis Dashboard",
    title_x=0.5,
    hovermode="x unified",
    # Enable crossfilter-style interactions
    dragmode="zoom",
    # Add spike lines for better cross-subplot interaction
    hoverdistance=100,
    spikedistance=1000,
)

# Add range selector buttons for easy navigation to the main x-axis
fig.update_xaxes(
    rangeselector=dict(buttons=list([dict(count=50, label="50 bars", step="all", stepmode="backward"), dict(count=100, label="100 bars", step="all", stepmode="backward"), dict(count=200, label="200 bars", step="all", stepmode="backward"), dict(step="all", label="All")])),
    rangeslider=dict(visible=False),  # Disable range slider for cleaner look
    row=1,
    col=1,  # Apply to the first subplot
)

# Update x-axes labels
fig.update_xaxes(title_text="Bar Index", row=3, col=1)

# Update y-axes labels
fig.update_yaxes(title_text="Price", row=1, col=1)
fig.update_yaxes(title_text="Fill Price", row=2, col=1)
fig.update_yaxes(title_text="FDUSD", row=3, col=1)

# Add grid to all subplots and enable spike lines for cross-subplot interaction
fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor="lightgray", showspikes=True, spikesnap="cursor", spikemode="across", spikethickness=1, spikecolor="rgba(0,0,0,0.5)", spikedash="solid")
fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor="lightgray", showspikes=True, spikesnap="cursor", spikemode="across", spikethickness=1, spikecolor="rgba(0,0,0,0.3)", spikedash="dot")

# Enable auto range breaks for better time series handling
if bar_indices or ohlc_data:
    all_bar_indices = bar_indices + [row["bar_idx"] for row in ohlc_data] if ohlc_data else bar_indices
    if all_bar_indices:
        # Set custom tick spacing for better readability
        start = min(all_bar_indices)
        end = max(all_bar_indices)
        tick_vals = list(range(start, end + 500, 500))
        fig.update_xaxes(tickvals=tick_vals)

        # Apply auto range breaks functionality
        fig = add_auto_rangebreaks(fig, sorted(set(all_bar_indices)))

# Save plots
log_dir = os.path.dirname(log_file)
ohlc_output_path = os.path.join(log_dir, "ohlc_trades_plot.html")
fig.write_html(ohlc_output_path)

# Show interactive plot
fig.show()

print(f"Plots saved to:")
print(f"- OHLC and trades: {ohlc_output_path}")
if ohlc_data:
    print(f"\nProcessed {len(ohlc_data)} OHLC bars")
if trade_events:
    print(f"Processed {len(trade_events)} trade events")
    buy_count = len([e for e in trade_events if e["is_buy"]])
    sell_count = len([e for e in trade_events if not e["is_buy"]])
    open_count = len([e for e in trade_events if e["is_open"]])
    print(f"- {buy_count} buy orders, {sell_count} sell orders, {open_count} open positions")
