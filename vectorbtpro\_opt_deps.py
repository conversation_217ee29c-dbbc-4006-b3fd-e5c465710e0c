# ==================================== VBTPROXYZ ====================================
# Copyright (c) 2021-2025 Oleg Polakow. All rights reserved.
#
# This file is part of the proprietary VectorBT® PRO package and is licensed under
# the VectorBT® PRO License available at https://vectorbt.pro/terms/software-license/
#
# Unauthorized publishing, distribution, sublicensing, or sale of this software
# or its parts is strictly prohibited.
# ===================================================================================

"""Optional dependencies for internal use."""

from vectorbtpro.utils.config import HybridConfig

__all__ = []

__pdoc__ = {}

opt_dep_config = HybridConfig(
    dict(
        yfinance=dict(
            link="https://pypi.org/project/yfinance/",
            version=">=0.2.54",
        ),
        binance=dict(
            dist_name="python-binance",
            link="https://pypi.org/project/python-binance/",
            version=">=1.0.16",
        ),
        ccxt=dict(
            link="https://pypi.org/project/ccxt/",
            version=">=1.89.14",
        ),
        ta=dict(
            link="https://pypi.org/project/ta/",
        ),
        pandas_ta=dict(
            link="https://pypi.org/project/pandas-ta/",
        ),
        talib=dict(
            dist_name="TA-Lib",
            link="https://pypi.org/project/TA-Lib/",
        ),
        bottleneck=dict(
            link="https://pypi.org/project/Bottleneck/",
        ),
        numexpr=dict(
            link="https://pypi.org/project/numexpr/",
        ),
        ray=dict(
            link="https://pypi.org/project/ray/",
            version=">=1.4.1",
        ),
        dask=dict(
            link="https://pypi.org/project/dask/",
        ),
        matplotlib=dict(
            link="https://pypi.org/project/matplotlib/",
            version=">=3.2.0",
        ),
        plotly=dict(
            link="https://pypi.org/project/plotly/",
            version=">=5.0.0",
        ),
        ipywidgets=dict(
            link="https://pypi.org/project/ipywidgets/",
            version=">=7.0.0",
        ),
        kaleido=dict(
            link="https://pypi.org/project/kaleido/",
        ),
        telegram=dict(
            dist_name="python-telegram-bot",
            link="https://pypi.org/project/python-telegram-bot/",
            version=">=13.4",
        ),
        quantstats=dict(
            link="https://pypi.org/project/QuantStats/",
            version=">=0.0.37",
        ),
        dill=dict(
            link="https://pypi.org/project/dill/",
        ),
        alpaca=dict(
            dist_name="alpaca-py",
            link="https://pypi.org/project/alpaca-py/",
        ),
        polygon=dict(
            dist_name="polygon-api-client",
            link="https://pypi.org/project/polygon-api-client/",
            version=">=1.0.0",
        ),
        bs4=dict(
            dist_name="beautifulsoup4",
            link="https://pypi.org/project/beautifulsoup4/",
        ),
        nasdaqdatalink=dict(
            dist_name="Nasdaq-Data-Link",
            link="https://pypi.org/project/Nasdaq-Data-Link/",
        ),
        pypfopt=dict(
            dist_name="PyPortfolioOpt",
            link="https://pypi.org/project/pyportfolioopt/",
            version=">=1.5.1",
        ),
        universal=dict(
            dist_name="universal-portfolios",
            link="https://pypi.org/project/universal-portfolios/",
        ),
        plotly_resampler=dict(
            dist_name="plotly-resampler",
            link="https://pypi.org/project/plotly-resampler/",
        ),
        technical=dict(
            link="https://pypi.org/project/technical/",
        ),
        riskfolio=dict(
            dist_name="Riskfolio-Lib",
            link="https://pypi.org/project/Riskfolio-Lib/",
            version=">=3.3.0",
        ),
        pathos=dict(
            link="https://pypi.org/project/pathos/",
        ),
        lz4=dict(
            link="https://pypi.org/project/lz4/",
        ),
        blosc=dict(
            link="https://pypi.org/project/blosc/",
        ),
        blosc2=dict(
            link="https://pypi.org/project/blosc2/",
        ),
        tables=dict(
            link="https://pypi.org/project/tables/",
        ),
        optuna=dict(
            link="https://pypi.org/project/optuna/",
        ),
        sqlalchemy=dict(
            dist_name="SQLAlchemy",
            link="https://pypi.org/project/SQLAlchemy/",
            version=">=2.0.0",
        ),
        mpire=dict(
            link="https://pypi.org/project/mpire/",
        ),
        duckdb=dict(
            link="https://pypi.org/project/duckdb/",
        ),
        duckdb_engine=dict(
            dist_name="duckdb-engine",
            link="https://pypi.org/project/duckdb-engine/",
        ),
        pyarrow=dict(
            link="https://pypi.org/project/pyarrow/",
        ),
        fastparquet=dict(
            link="https://pypi.org/project/fastparquet/",
        ),
        tabulate=dict(
            link="https://pypi.org/project/tabulate/",
        ),
        alpha_vantage=dict(
            link="https://pypi.org/project/alpha_vantage/",
            version=">=3.0.0",
        ),
        databento=dict(
            link="https://pypi.org/project/databento/",
        ),
        smartmoneyconcepts=dict(
            link="https://pypi.org/project/smartmoneyconcepts/",
        ),
        findatapy=dict(
            link="https://pypi.org/project/findatapy/",
        ),
        github=dict(
            dist_name="PyGithub",
            link="https://pypi.org/project/PyGithub/",
            version=">=1.59.0",
        ),
        jmespath=dict(
            link="https://pypi.org/project/jmespath/",
        ),
        jsonpath_ng=dict(
            dist_name="jsonpath-ng",
            link="https://pypi.org/project/jsonpath-ng/",
        ),
        fuzzysearch=dict(
            link="https://pypi.org/project/fuzzysearch/",
        ),
        rapidfuzz=dict(
            link="https://pypi.org/project/rapidfuzz/",
        ),
        nestedtext=dict(
            link="https://pypi.org/project/nestedtext/",
        ),
        yaml=dict(
            dist_name="PyYAML",
            link="https://pypi.org/project/PyYAML/",
        ),
        ruamel=dict(
            dist_name="ruamel.yaml",
            link="https://pypi.org/project/ruamel.yaml/",
        ),
        toml=dict(
            link="https://pypi.org/project/toml/",
        ),
        markdown=dict(
            link="https://pypi.org/project/markdown/",
        ),
        pygments=dict(
            link="https://pypi.org/project/Pygments/",
        ),
        IPython=dict(
            dist_name="ipython",
            link="https://pypi.org/project/ipython/",
        ),
        pymdownx=dict(
            dist_name="pymdown-extensions",
            link="https://pypi.org/project/pymdown-extensions/",
        ),
        openai=dict(
            link="https://pypi.org/project/openai/",
        ),
        litellm=dict(
            link="https://pypi.org/project/litellm/",
        ),
        llama_index=dict(
            dist_name="llama-index",
            link="https://pypi.org/project/llama-index/",
        ),
        tiktoken=dict(
            link="https://pypi.org/project/tiktoken/",
        ),
        lmdbm=dict(
            link="https://pypi.org/project/lmdbm/",
        ),
        bm25s=dict(
            link="https://pypi.org/project/bm25s/",
        ),
        PyStemmer=dict(
            link="https://pypi.org/project/PyStemmer/",
        ),
        pyperclip=dict(
            link="https://pypi.org/project/pyperclip/",
        )
    )
)
"""_"""

__pdoc__[
    "opt_dep_config"
] = f"""Config for optional packages.

```python
{opt_dep_config.prettify_doc()}
```
"""
