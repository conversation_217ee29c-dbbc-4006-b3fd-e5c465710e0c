{"cells": [{"cell_type": "markdown", "id": "d2ac023c-531a-487a-81f0-fcce3c9925b6", "metadata": {}, "source": ["# SuperFast SuperTrend"]}, {"cell_type": "markdown", "id": "e052b3c7-c1ec-4245-8025-fbacbc71e1a6", "metadata": {}, "source": ["## Data"]}, {"cell_type": "code", "execution_count": 1, "id": "8ea9cb06-d59c-4c2c-807a-23854fa16676", "metadata": {}, "outputs": [], "source": ["from vectorbtpro import *\n", "# whats_imported()\n", "\n", "vbt.settings.set_theme('dark')"]}, {"cell_type": "code", "execution_count": 2, "id": "7f8c4e61-3d13-4659-b559-3cef404adfb4", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bb473dfb036a4a5692ac6029b2906514", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62cfbae8dd33498d8fb8720cc27fe65d", "version_major": 2, "version_minor": 0}, "text/plain": ["0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0a58005198f942abab4c4514ea5b10f4", "version_major": 2, "version_minor": 0}, "text/plain": ["0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# data = vbt.BinanceData.pull(\n", "#     ['BTCUSDT', 'ETHUSDT'], \n", "#     start='2020-01-01 UTC',\n", "#     end='2022-01-01 UTC',\n", "#     timeframe='1h'\n", "# )"]}, {"cell_type": "code", "execution_count": 3, "id": "508e2929-eab9-4db9-b581-d3a78959e173", "metadata": {}, "outputs": [], "source": ["# data.to_hdf('my_data.h5')"]}, {"cell_type": "code", "execution_count": 4, "id": "5f4587b7-a9d2-4765-a52d-1ea9fa32bdbf", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5aaef51caac64cba9234e5f13d416a6e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = vbt.HDFData.pull('my_data.h5')"]}, {"cell_type": "code", "execution_count": 5, "id": "87de3e25-9836-45a4-9452-a3a0976a1d5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 17513 entries, 2020-01-01 00:00:00+00:00 to 2021-12-31 23:00:00+00:00\n", "Data columns (total 9 columns):\n", " #   Column              Non-Null Count  Dtype  \n", "---  ------              --------------  -----  \n", " 0   Open                17513 non-null  float64\n", " 1   High                17513 non-null  float64\n", " 2   Low                 17513 non-null  float64\n", " 3   Close               17513 non-null  float64\n", " 4   Volume              17513 non-null  float64\n", " 5   Quote volume        17513 non-null  float64\n", " 6   Trade count         17513 non-null  int64  \n", " 7   Taker base volume   17513 non-null  float64\n", " 8   Taker quote volume  17513 non-null  float64\n", "dtypes: float64(8), int64(1)\n", "memory usage: 1.3 MB\n"]}], "source": ["data.data['BTCUSDT'].info()"]}, {"cell_type": "code", "execution_count": 6, "id": "9389c053-5429-4fe7-a05f-cd2961ebaf94", "metadata": {}, "outputs": [{"data": {"text/plain": ["Start                   2020-01-01 00:00:00+00:00\n", "End                     2021-12-31 23:00:00+00:00\n", "Period                                      17513\n", "Total Symbols                                   2\n", "Last Index: BTCUSDT     2021-12-31 23:00:00+00:00\n", "Last Index: ETHUSDT     2021-12-31 23:00:00+00:00\n", "Null Counts: BTCUSDT                            0\n", "Null Counts: ETHUSDT                            0\n", "Name: agg_stats, dtype: object"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data.stats()"]}, {"cell_type": "code", "execution_count": 7, "id": "b10cf7a1-88a5-42d4-820b-ae7752ab85f6", "metadata": {}, "outputs": [], "source": ["high = data.get('High')\n", "low = data.get('Low')\n", "close = data.get('Close')"]}, {"cell_type": "code", "execution_count": 8, "id": "d086a72b-de2e-4b0f-875d-54de8753b1ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["symbol                      BTCUSDT  ETHUSDT\n", "Open time                                   \n", "2020-01-01 00:00:00+00:00   7177.02   128.87\n", "2020-01-01 01:00:00+00:00   7216.27   130.64\n", "2020-01-01 02:00:00+00:00   7242.85   130.85\n", "2020-01-01 03:00:00+00:00   7225.01   130.20\n", "2020-01-01 04:00:00+00:00   7217.27   130.20\n", "...                             ...      ...\n", "2021-12-31 19:00:00+00:00  45728.28  3626.27\n", "2021-12-31 20:00:00+00:00  45879.24  3645.04\n", "2021-12-31 21:00:00+00:00  46333.86  3688.41\n", "2021-12-31 22:00:00+00:00  46303.99  3681.80\n", "2021-12-31 23:00:00+00:00  46216.93  3676.23\n", "\n", "[17513 rows x 2 columns]\n"]}], "source": ["print(close)"]}, {"cell_type": "markdown", "id": "cc9fb395-725a-4376-8d89-f3cfbc0fb9dd", "metadata": {}, "source": ["## Design"]}, {"cell_type": "markdown", "id": "94ad1367-4ca1-4e8a-8628-39242e93ceea", "metadata": {}, "source": ["### Pandas"]}, {"cell_type": "code", "execution_count": 10, "id": "b94872b9-23db-42d4-b5d5-ec15176c4f6e", "metadata": {}, "outputs": [], "source": ["def get_med_price(high, low):\n", "    return (high + low) / 2"]}, {"cell_type": "code", "execution_count": 11, "id": "45fdd6c7-4566-4468-8872-6f1f6c62991e", "metadata": {}, "outputs": [], "source": ["def get_atr(high, low, close, period):\n", "    tr0 = abs(high - low)\n", "    tr1 = abs(high - close.shift())\n", "    tr2 = abs(low - close.shift())\n", "    tr = pd.concat((tr0, tr1, tr2), axis=1).max(axis=1)\n", "    atr = tr.ewm(alpha=1 / period, adjust=False, min_periods=period).mean()\n", "    return atr"]}, {"cell_type": "code", "execution_count": 12, "id": "37baaa9e-901d-4f49-a143-a9e1a24a20e7", "metadata": {}, "outputs": [], "source": ["def get_basic_bands(med_price, atr, multiplier):\n", "    matr = multiplier * atr\n", "    upper = med_price + matr\n", "    lower = med_price - matr\n", "    return upper, lower"]}, {"cell_type": "code", "execution_count": 13, "id": "b67ade24-1bec-411e-b570-e54ab89a47c5", "metadata": {}, "outputs": [], "source": ["def get_final_bands(close, upper, lower):\n", "    trend = pd.Series(np.full(close.shape, np.nan), index=close.index)\n", "    dir_ = pd.Series(np.full(close.shape, 1), index=close.index)\n", "    long = pd.Series(np.full(close.shape, np.nan), index=close.index)\n", "    short = pd.Series(np.full(close.shape, np.nan), index=close.index)\n", "\n", "    for i in range(1, close.shape[0]):\n", "        if close.iloc[i] > upper.iloc[i - 1]:\n", "            dir_.iloc[i] = 1\n", "        elif close.iloc[i] < lower.iloc[i - 1]:\n", "            dir_.iloc[i] = -1\n", "        else:\n", "            dir_.iloc[i] = dir_.iloc[i - 1]\n", "            if dir_.iloc[i] > 0 and lower.iloc[i] < lower.iloc[i - 1]:\n", "                lower.iloc[i] = lower.iloc[i - 1]\n", "            if dir_.iloc[i] < 0 and upper.iloc[i] > upper.iloc[i - 1]:\n", "                upper.iloc[i] = upper.iloc[i - 1]\n", "\n", "        if dir_.iloc[i] > 0:\n", "            trend.iloc[i] = long.iloc[i] = lower.iloc[i]\n", "        else:\n", "            trend.iloc[i] = short.iloc[i] = upper.iloc[i]\n", "            \n", "    return trend, dir_, long, short"]}, {"cell_type": "code", "execution_count": 14, "id": "eb3af20c-d59e-4bef-854e-b59bad25acce", "metadata": {}, "outputs": [], "source": ["def supertrend(high, low, close, period=7, multiplier=3):\n", "    med_price = get_med_price(high, low)\n", "    atr = get_atr(high, low, close, period)\n", "    upper, lower = get_basic_bands(med_price, atr, multiplier)\n", "    return get_final_bands(close, upper, lower)"]}, {"cell_type": "code", "execution_count": 15, "id": "ac2ba411-f0e2-49ba-87b0-c2510e0d35f0", "metadata": {}, "outputs": [], "source": ["supert, superd, superl, supers = supertrend(\n", "    high['BTCUSDT'], \n", "    low['BTCUSDT'], \n", "    close['BTCUSDT']\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "7baab77d-8681-49ac-af7e-7ca5671adbb2", "metadata": {}, "outputs": [{"data": {"text/plain": ["Open time\n", "2020-01-01 00:00:00+00:00             NaN\n", "2020-01-01 01:00:00+00:00             NaN\n", "2020-01-01 02:00:00+00:00             NaN\n", "2020-01-01 03:00:00+00:00             NaN\n", "2020-01-01 04:00:00+00:00             NaN\n", "                                 ...     \n", "2021-12-31 19:00:00+00:00    47858.398491\n", "2021-12-31 20:00:00+00:00    47608.346563\n", "2021-12-31 21:00:00+00:00    47608.346563\n", "2021-12-31 22:00:00+00:00    47608.346563\n", "2021-12-31 23:00:00+00:00    47608.346563\n", "Length: 17513, dtype: float64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["supert"]}, {"cell_type": "code", "execution_count": 17, "id": "6d8c9e67-84e0-4102-8c33-1b3b6fc3ad0e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Open time\n", "2020-01-01 00:00:00+00:00    1\n", "2020-01-01 01:00:00+00:00    1\n", "2020-01-01 02:00:00+00:00    1\n", "2020-01-01 03:00:00+00:00    1\n", "2020-01-01 04:00:00+00:00    1\n", "                            ..\n", "2021-12-31 19:00:00+00:00   -1\n", "2021-12-31 20:00:00+00:00   -1\n", "2021-12-31 21:00:00+00:00   -1\n", "2021-12-31 22:00:00+00:00   -1\n", "2021-12-31 23:00:00+00:00   -1\n", "Length: 17513, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["superd"]}, {"cell_type": "code", "execution_count": 18, "id": "bde7f7ec-7564-447a-bf48-27873ff72a8a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Open time\n", "2020-01-01 00:00:00+00:00   NaN\n", "2020-01-01 01:00:00+00:00   NaN\n", "2020-01-01 02:00:00+00:00   NaN\n", "2020-01-01 03:00:00+00:00   NaN\n", "2020-01-01 04:00:00+00:00   NaN\n", "                             ..\n", "2021-12-31 19:00:00+00:00   NaN\n", "2021-12-31 20:00:00+00:00   NaN\n", "2021-12-31 21:00:00+00:00   NaN\n", "2021-12-31 22:00:00+00:00   NaN\n", "2021-12-31 23:00:00+00:00   NaN\n", "Length: 17513, dtype: float64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["superl"]}, {"cell_type": "code", "execution_count": 19, "id": "f21db639-c324-4325-b9b9-0def69c37497", "metadata": {}, "outputs": [{"data": {"text/plain": ["Open time\n", "2020-01-01 00:00:00+00:00             NaN\n", "2020-01-01 01:00:00+00:00             NaN\n", "2020-01-01 02:00:00+00:00             NaN\n", "2020-01-01 03:00:00+00:00             NaN\n", "2020-01-01 04:00:00+00:00             NaN\n", "                                 ...     \n", "2021-12-31 19:00:00+00:00    47858.398491\n", "2021-12-31 20:00:00+00:00    47608.346563\n", "2021-12-31 21:00:00+00:00    47608.346563\n", "2021-12-31 22:00:00+00:00    47608.346563\n", "2021-12-31 23:00:00+00:00    47608.346563\n", "Length: 17513, dtype: float64"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["supers"]}, {"cell_type": "code", "execution_count": 20, "id": "44cdaed3-f1a3-4202-8d74-fb9509aca2c8", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(33, 34, 44); fill-opacity: 1;\"/><defs id=\"defs-a90c68\"><g class=\"clips\"><clipPath id=\"clipa90c68xyplot\" class=\"plotclip\"><rect width=\"626\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa90c68x\"><rect x=\"44\" y=\"0\" width=\"626\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa90c68y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa90c68xy\"><rect x=\"44\" y=\"46\" width=\"626\" height=\"261\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"minor-gridlayer\"><g class=\"x\"/><g class=\"y\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(122.35,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(259.47,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(396.58,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(533.7,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,280.4)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,237.43)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,194.45)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,151.48000000000002)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,108.5)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,65.53)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(44,46)\" clip-path=\"url(#clipa90c68xyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace5e70cbaf-7382-4ca8-ac82-e4a249b5d5fb\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,219.19L1.63,213.53L2.45,215.06L3.26,215.73L4.08,215.13L4.9,215.01L5.71,216.37L7.35,218.18L8.16,217.04L8.98,217.45L9.79,215.01L11.43,215.37L12.24,214.27L13.06,213.31L13.87,214.05L14.69,213.72L15.51,214.68L17.14,214.36L17.96,217.38L19.59,216.26L20.4,217.99L22.04,223.26L22.85,222.18L24.49,222.72L25.3,224.9L26.93,220.48L27.75,220.49L28.57,222.39L29.38,220.8L30.2,223.13L31.01,222.75L31.83,223.14L32.65,229.9L33.46,236.98L34.28,236.63L35.1,235.84L35.91,238.83L36.73,236.32L37.54,236.57L38.36,237.35L39.18,239.82L39.99,243.95L40.81,238.11L41.62,238.52L42.44,216.34L43.26,215.45L44.07,217.02L44.89,216.94L45.71,212L46.52,204.46L47.34,207.56L48.15,205.14L48.97,206.66L49.79,204.42L50.6,212.51L52.23,201.22L53.05,203.87L55.5,207.16L56.32,210.08L57.13,208.57L57.95,204.75L58.76,208.42L59.58,207.83L61.21,205.77L62.03,205.62L63.66,203.89L64.48,204.48L65.29,204.61L66.11,203.13L66.93,207.04L67.74,205.48L69.37,208.28L70.19,207.18L71.01,206.98L71.82,208.01L72.64,204.15L73.46,208.02L75.09,205.09L75.9,204.26L77.54,203.97L78.35,202.3L79.17,194.94L79.98,196.26L80.8,193.49L81.62,195.11L84.07,194.55L84.88,197.44L85.7,197.64L86.51,198.72L87.33,198.1L88.15,195.12L88.96,197.34L89.78,197.29L90.59,194.14L91.41,194.75L92.23,194.47L93.04,196.69L93.86,196.23L94.68,197.31L96.31,203.83L97.12,203.57L97.94,201.85L98.76,198L99.57,187.01L100.39,187.91L101.2,188.89L102.02,188.6L103.65,190.75L104.47,187.34L106.1,186.17L106.92,187.58L107.73,186.49L108.55,188.97L109.37,187.31L110.18,189.71L111,188.91L111.81,186.4L112.63,188.59L113.45,188L114.26,187.02L115.08,184.69L117.53,158.04L118.34,156.56L119.98,157.49L120.79,162.13L122.43,159.38L123.24,158.48L124.06,158.43L124.87,160.81L126.51,157.47L127.32,159.71L128.95,162.31L129.77,168.69L131.4,153.75L132.22,145.21L133.04,142.12L133.85,134.47L134.67,147.04L135.48,144.19L136.3,135.97L137.12,111.34L138.75,124.51L139.56,122.64L140.38,118.83L141.2,119.68L142.01,119.3L142.83,122.9L143.65,121.63L144.46,122.91L146.09,119.88L146.91,123.86L147.73,115.36L148.54,121.72L149.36,122.15L150.17,138.57L151.81,143.84L152.62,151.6L153.44,144.5L154.26,147.07L155.07,141.91L155.89,143.64L156.7,152.19L157.52,148.04L159.15,152.34L159.97,151.3L161.6,156.32L162.42,152.76L163.23,154.2L164.05,159.42L164.87,157.36L165.68,158.89L166.5,156.97L167.31,157.26L168.13,158.29L168.95,156.44L170.58,165.76L171.4,164.73L172.21,155.54L173.03,164.07L173.84,166.51L174.66,164.67L176.29,163.92L177.11,165.21L177.92,165.33L178.74,164.09L179.56,166.05L180.37,170.04L182.01,169.68L182.82,173.68L183.64,172.82L184.45,168.06L186.09,157.56L186.9,159.59L188.53,142.73L189.35,152.52L190.17,151.35L190.98,146.55L192.62,142.16L193.43,144.16L194.25,140.71L195.06,131.52L195.88,133.93L196.7,130.84L197.51,130.73L198.33,134.83L199.96,141.28L200.78,137.71L202.41,143.9L203.23,138.64L204.86,144.5L205.67,143.13L206.49,140.72L207.31,143.83L208.94,133.89L209.75,134.65L210.57,133.92L211.39,135.41L212.2,134.55L213.02,139.43L213.84,141.36L214.65,146.73L216.28,142.82L217.1,140.29L217.92,140.6L218.73,142.11L219.55,139.34L220.37,141.25L221.18,140.07L222,136.37L222.81,136.63L223.63,137.96L224.45,138.65L225.26,137.09L226.08,134.27L226.89,136.66L227.71,135.33L228.53,139.69L229.34,139.73L230.16,138.2L231.79,136.53L232.61,139.66L234.24,132.55L235.06,135.33L235.87,135.05L236.69,138.73L237.5,139.36L238.32,138.28L239.95,143.03L240.77,141.98L241.59,137.28L242.4,138.68L243.22,137.65L244.03,142.01L244.85,140.47L245.67,140.99L247.3,141.16L248.11,139L248.93,140.01L249.75,139.18L250.56,138.84L251.38,134.97L253.83,138.97L254.64,125.25L255.46,111.34L256.28,111.4L257.09,108.76L257.91,103.42L258.72,107.42L259.54,104.66L261.99,105.07L262.81,103.92L263.62,103.17L264.44,105.48L266.07,89.15L266.89,90.4L267.7,90.26L268.52,81.98L270.15,82.48L270.97,89.3L271.78,85.29L272.6,85.21L273.42,78.83L274.23,82.03L275.05,81.58L275.86,78.5L277.5,97.1L278.31,95.83L279.13,89.96L279.95,94.55L280.76,93.59L281.58,83.56L282.39,79.78L283.21,85.71L284.03,82.73L284.84,71.96L285.66,74.87L286.47,82.9L288.11,79.95L288.92,84.26L289.74,83L290.56,78.47L292.19,74.54L293,77.85L295.45,92.59L296.27,93.44L297.08,92.43L297.9,88.24L298.72,86.46L299.53,90.01L300.35,90.4L301.17,92.98L301.98,91.9L302.8,86.15L303.61,86.57L304.43,88.69L305.25,89.48L306.06,87.43L306.88,91.71L307.69,90.17L308.51,89.65L309.33,87.86L310.14,84.51L310.96,89.05L311.78,86.54L312.59,86.57L313.41,89.12L314.22,84.98L315.86,80.86L316.67,78.38L317.49,71.85L318.31,74.3L319.12,68.18L319.94,68.62L320.75,65.17L321.57,67.92L322.39,70.8L323.2,78.7L324.02,75.31L324.83,81.52L326.47,73.16L327.28,71.87L328.1,69.37L328.92,72.84L329.73,72.85L330.55,69.18L331.36,67.7L332.18,69.96L333,64.31L333.81,67.55L334.63,69.28L335.44,68.86L336.26,75.93L337.08,75.81L337.89,74.13L338.71,75.94L341.16,72.21L341.97,72.84L343.61,69.38L344.42,69.16L346.05,72.54L346.87,70.67L347.69,72.23L348.5,70.42L349.32,70.45L350.14,69.36L350.95,66.43L351.77,69.73L352.58,64.84L353.4,49.95L355.03,48.96L355.85,49.7L356.66,51.23L357.48,56.22L359.11,53.64L359.93,57.26L360.75,59.18L361.56,89.88L362.38,94.47L363.19,94.39L364.01,95.41L364.83,94.14L366.46,91.83L367.28,92.52L368.91,92.79L369.72,86.39L370.54,93.15L371.36,88.14L372.99,92.54L373.8,91.88L374.62,91.08L375.44,93.32L377.07,89.66L377.89,91.28L378.7,93.38L379.52,91.65L380.33,92.27L381.15,95.57L381.97,98.34L382.78,96.31L384.41,90.4L385.23,90.99L386.05,93.29L386.86,91.31L389.31,88.91L390.13,92.17L390.94,93.24L391.76,93.06L394.21,90.51L395.02,90.8L397.47,93.9L398.29,91.85L399.11,94.29L399.92,91.79L401.55,91.64L402.37,91.52L404.82,93.06L405.63,92.71L406.45,93.96L407.27,102.74L408.08,86.68L408.9,85.71L410.53,85.19L411.35,84.94L413.8,86.93L414.61,84.85L415.43,86.64L416.25,83.64L417.06,84.83L417.88,90.12L418.69,89.47L419.51,91.44L421.14,91.34L421.96,93.02L423.59,92.12L424.41,89.45L425.22,88.92L426.04,90.9L427.67,91.18L428.49,91.64L429.3,91.96L430.12,89.8L430.94,91.5L431.75,96.89L432.57,95.97L433.38,97.12L434.2,97.66L435.02,100.07L435.83,102.46L436.65,98.45L438.28,110.56L439.1,110.21L440.73,112.31L441.55,118.37L442.36,117.25L443.18,117.56L443.99,117.97L444.81,120.45L445.63,119.28L446.44,116.57L447.26,116.45L448.08,114.48L450.52,113.3L451.34,117.51L452.16,122.6L452.97,120.9L453.79,122.09L454.6,120.79L455.42,118.74L456.24,119.23L457.05,119.87L457.87,126.09L458.69,127.35L459.5,112.98L462.77,104.92L463.58,108.99L466.03,105.58L466.85,106.63L467.66,104.67L468.48,108.32L469.3,110.72L470.11,121.85L470.93,123.85L471.74,120.5L472.56,122.87L473.38,121.09L475.01,120.09L475.83,121.28L476.64,117.3L477.46,118.49L479.91,123.33L480.72,120.58L481.54,121.23L482.35,119.65L483.17,118.4L483.99,119.24L484.8,119.14L485.62,117.26L486.44,116.8L487.25,114.85L489.7,120.44L490.52,118.9L491.33,120.55L492.15,116.75L492.96,116.89L493.78,116.03L494.6,117.97L495.41,115.35L497.05,112.36L497.86,110.07L498.68,107.27L499.49,109.15L501.13,109.39L501.94,108.94L503.57,98.7L504.39,98.6L506.02,99.61L506.84,96.81L507.66,98.8L508.47,95.59L509.29,91.1L510.1,91.61L510.92,92.11L511.74,90.86L512.55,92.79L513.37,92.3L514.19,91.24L515,91.9L515.82,96.04L516.63,93.27L517.45,93.95L518.27,85.22L519.08,84.29L519.9,84.88L522.35,80.42L523.16,82.52L524.8,68.34L525.61,63.65L526.43,65.52L527.24,70.89L528.06,70.45L528.88,56.55L529.69,51.08L530.51,52.21L531.32,52.88L532.14,57.78L532.96,57.42L533.77,61.1L534.59,59.07L535.41,63.49L536.22,64.64L537.04,60.86L537.85,55.44L538.67,58.28L539.49,55.08L540.3,59.59L541.12,60.39L541.93,65.15L544.38,61.57L545.2,57.22L546.02,58.15L546.83,51.07L547.65,30.34L548.46,34.04L549.28,32.92L550.1,34.73L550.91,32.98L551.73,33.81L552.54,31.99L553.36,33.9L554.99,29.23L555.81,33.93L556.63,27.44L557.44,36.45L558.26,38.43L559.07,34.65L559.89,34.22L560.71,38.01L561.52,31.46L562.34,32.95L563.97,28.64L564.79,29.29L565.6,30.02L566.42,33.11L568.05,38.58L568.87,37.03L569.68,40.84L570.5,38.41L571.32,34.18L572.13,35.17L572.95,34.54L573.77,28.9L576.21,33.77L577.03,33.43L578.66,29.24L579.48,32.44L580.29,29.25L581.11,30.62L582.74,19.59L583.56,18.94L584.38,13.05L585.19,13.85L586.01,14.05L586.82,18.39L587.64,21.77L588.46,21.38L589.27,24.31L590.09,24.34L590.9,26.74L591.72,32.81L592.54,29.9L593.35,30.31L594.17,29.99L594.99,31.6L596.62,34.28L597.43,38.24L599.07,39.88L599.88,33.35L601.51,38.22L602.33,35.11L603.15,36.49L603.96,34.91L605.6,27.92L606.41,32.17L608.86,24.52L609.68,25.35L611.31,29.75L612.13,28.29L612.94,26.15L613.76,29.45L614.57,29.17L615.39,29.88L617.02,35.68L617.84,32.82L618.65,32.41L619.47,33.8L620.29,27.63L621.1,29.72L621.92,28.96L622.74,30.95L623.55,29.72L624.37,29.84L625.18,30.03L626,29.45\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace15e281d1-6a63-4e53-9bdb-8af087cbe3f6\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M22.04,213.9L24.49,214.69L25.3,215.6L32.65,215.6L33.46,220.13L35.1,222.6L35.91,224.33L37.54,225.36L38.36,226.8L40.81,229.8L41.62,229.8\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M150.99,107.63L153.44,117.32L154.26,117.32L155.89,117.32L156.7,118.21L158.34,121.02L159.15,126.08L161.6,134.27L162.42,134.27L163.23,134.99L164.05,138.13L164.87,138.25L165.68,140.56L168.95,141.13L169.76,142.7L171.4,148.09L172.21,148.09L178.74,148.3L179.56,149.2L182.01,154.78L182.82,156.4L186.9,156.4\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M361.56,48.65L363.19,65.93L364.01,65.93L364.83,68.88L365.64,68.88L367.28,71.99L368.09,74.15L369.72,75.6L370.54,75.6L379.52,76.35L380.33,77.38L382.78,81.16L383.6,81.16L396.66,81.85L397.47,83L404.82,83.67L405.63,84.25L408.08,86.36\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M431.75,82.33L432.57,85.21L433.38,85.27L434.2,86.1L435.02,88.6L437.47,88.63L438.28,89.7L439.1,93.29L439.91,93.29L440.73,93.29L441.55,95.91L442.36,98.24L443.18,98.24L444.81,100.95L445.63,102.85L451.34,102.85L452.16,104.71L453.79,104.86L454.6,105.57L456.24,106.27L457.05,107.09L458.69,110.04L459.5,110.04L460.32,110.04\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M470.11,96.9L470.93,103.98L471.74,103.98L472.56,103.98L473.38,105.16L478.27,105.66L479.09,106.66L480.72,107.9L481.54,107.9L483.99,107.9L484.8,109.01L497.86,109.37\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M597.43,18.66L598.25,21.38L599.07,21.38L626,21.38\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter traced8bbd6fc-40ad-4c52-acfc-c0782c6c2dd2\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M4.9,220.39L21.22,220.3\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M42.44,247.95L44.07,237.08L44.89,235.79L47.34,227.63L48.15,226.07L50.6,223.17L51.42,223.17L60.4,222.57L61.21,219.78L62.03,219.08L62.84,217.05L64.48,215.71L65.29,215.59L66.93,214.53L67.74,214.53L77.54,214.53L78.35,213.35L80.8,207.88L81.62,206.99L89.78,206.47L90.59,205.45L92.23,204.12L93.04,204.12L98.76,204.12L99.57,207.11L101.2,203.76L102.02,203.76L105.29,200.4L106.1,198.36L114.26,198.33L115.08,196.31L115.9,193.86L116.71,188.5L119.16,176.98L119.98,176.36L122.43,176.36L123.24,175.2L124.87,173.44L125.69,172.78L126.51,169.6L127.32,169.6L132.22,169.6L133.04,168.07L133.85,166.2L134.67,166.2L136.3,166.2L137.12,163.03L137.93,153.94L138.75,153.94L140.38,153.94L141.2,151.21L142.01,148.33L142.83,148.33L143.65,148.33L144.46,146.9L146.09,140.33L146.91,140.33L150.17,140.33\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M187.72,177.33L188.53,170.87L189.35,170.87L194.25,170.49L195.06,166.99L196.7,158.2L197.51,158.2L208.94,157.65L209.75,152.24L211.39,152.24L212.2,151.24L224.45,151.24L225.26,150.39L227.71,148.05L228.53,148.05L250.56,147.81L251.38,146.83L253.01,146.3L253.83,146.3L254.64,146.5L255.46,137.84L257.09,133.35L257.91,126.48L259.54,126.48L260.36,123.96L261.99,122.34L262.81,122.34L264.44,120.52L265.25,120.52L266.89,113.65L267.7,113.65L271.78,113.65L272.6,112.7L273.42,110.36L274.23,110.36L282.39,109.9L283.21,109.5L284.03,109.04L284.84,103.94L291.37,103.94L292.19,101.46L294.64,101.17L295.45,101.17L315.04,100.47L315.86,97.41L316.67,97.41L317.49,94.32L318.31,91.41L319.12,91.41L321.57,88.36L322.39,88.36L341.97,87.87L342.79,85.87L344.42,82.51L345.24,82.51L350.14,82.34L350.95,80.01L352.58,80.01L353.4,75.36L355.03,65.77L355.85,65.77L360.75,65.77\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M408.9,102.32L409.72,102.32L410.53,101.3L412.16,100.68L412.98,100.68L415.43,97.25L416.25,96.28L430.94,96.28\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M461.13,129.47L462.77,126.45L463.58,124.96L464.4,124.96L465.22,123.02L466.85,121.2L467.66,120.73L469.3,120.73\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M498.68,122.2L501.13,121.15L501.94,119.87L502.76,118.73L503.57,115.8L504.39,111.87L505.21,111.87L508.47,110.8L509.29,106.53L510.92,104.84L511.74,104.71L513.37,104.71L514.19,103.86L515.82,102.4L516.63,102.4L519.08,101.84L519.9,99.35L520.71,99.35L521.53,96.51L523.16,95.66L523.98,94.69L525.61,85.62L526.43,84.32L528.88,84.32L529.69,75.61L530.51,73.41L531.32,73.41L546.83,73.41L547.65,71.37L549.28,60.22L550.1,60.22L550.91,59.66L551.73,57.37L553.36,53.04L554.18,53.04L554.99,50.4L555.81,50.4L563.97,50.4L564.79,49.04L573.77,48.33L574.58,46.15L581.93,45.42L582.74,44.98L583.56,43.94L584.38,40.53L586.01,35.91L586.82,35.91L596.62,35.91\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(122.35,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 5</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(259.47,0)\">Jan 12</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(396.58,0)\">Jan 19</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(533.7,0)\">Jan 26</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" transform=\"translate(0,280.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">7000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,237.43)\">7500</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,194.45)\">8000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,151.48000000000002)\">8500</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,108.5)\">9000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,65.53)\">9500</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"smithlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-a90c68\"><g class=\"clips\"/><clipPath id=\"legenda90c68\"><rect width=\"230\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(440,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(33, 34, 44); fill-opacity: 1; stroke-width: 0px;\" width=\"230\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legenda90c68)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.859375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(77.359375,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Short</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"75.4375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(155.296875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Long</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"71.546875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["date_range = slice('2020-01-01', '2020-02-01')\n", "fig = close.loc[date_range, 'BTCUSDT'].rename('Close').vbt.plot()\n", "supers.loc[date_range].rename('Short').vbt.plot(fig=fig)\n", "superl.loc[date_range].rename('Long').vbt.plot(fig=fig).show_svg()"]}, {"cell_type": "code", "execution_count": 21, "id": "5fc5caf2-d308-4401-b36a-dcfdc6c16b55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.87 s ± 643 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "supertrend(high['BTCUSDT'], low['BTCUSDT'], close['BTCUSDT'])"]}, {"cell_type": "code", "execution_count": 22, "id": "a73d8c09-378b-4c27-b306-8ea61153207b", "metadata": {}, "outputs": [], "source": ["SUPERTREND = vbt.pandas_ta('SUPERTREND')"]}, {"cell_type": "code", "execution_count": 23, "id": "ed795d82-9324-413a-a343-9d2ad3b06750", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.3 s ± 53.5 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "SUPERTREND.run(high['BTCUSDT'], low['BTCUSDT'], close['BTCUSDT'])"]}, {"cell_type": "markdown", "id": "42175d89-0ee6-42c4-9ce0-4124603cf90c", "metadata": {}, "source": ["### NumPy + Numba"]}, {"cell_type": "code", "execution_count": 24, "id": "c401b752-fb8f-4e4f-a626-5b1bc1b00abf", "metadata": {}, "outputs": [], "source": ["def get_atr_np(high, low, close, period):\n", "    shifted_close = vbt.nb.fshift_1d_nb(close)\n", "    tr0 = np.abs(high - low)\n", "    tr1 = np.abs(high - shifted_close)\n", "    tr2 = np.abs(low - shifted_close)\n", "    tr = np.column_stack((tr0, tr1, tr2)).max(axis=1)\n", "    atr = vbt.nb.wwm_mean_1d_nb(tr, period)\n", "    return atr"]}, {"cell_type": "code", "execution_count": 25, "id": "5020dffd-48cb-4805-b258-5b6987792d1d", "metadata": {}, "outputs": [], "source": ["@njit\n", "def get_final_bands_nb(close, upper, lower):\n", "    trend = np.full(close.shape, np.nan)\n", "    dir_ = np.full(close.shape, 1)\n", "    long = np.full(close.shape, np.nan)\n", "    short = np.full(close.shape, np.nan)\n", "\n", "    for i in range(1, close.shape[0]):\n", "        if close[i] > upper[i - 1]:\n", "            dir_[i] = 1\n", "        elif close[i] < lower[i - 1]:\n", "            dir_[i] = -1\n", "        else:\n", "            dir_[i] = dir_[i - 1]\n", "            if dir_[i] > 0 and lower[i] < lower[i - 1]:\n", "                lower[i] = lower[i - 1]\n", "            if dir_[i] < 0 and upper[i] > upper[i - 1]:\n", "                upper[i] = upper[i - 1]\n", "\n", "        if dir_[i] > 0:\n", "            trend[i] = long[i] = lower[i]\n", "        else:\n", "            trend[i] = short[i] = upper[i]\n", "            \n", "    return trend, dir_, long, short"]}, {"cell_type": "code", "execution_count": 26, "id": "0db7eb3a-c3ac-4495-a91e-ba056c09b7af", "metadata": {}, "outputs": [], "source": ["def faster_supertrend(high, low, close, period=7, multiplier=3):\n", "    med_price = get_med_price(high, low)\n", "    atr = get_atr_np(high, low, close, period)\n", "    upper, lower = get_basic_bands(med_price, atr, multiplier)\n", "    return get_final_bands_nb(close, upper, lower)"]}, {"cell_type": "code", "execution_count": 27, "id": "1faaaafb-871c-46c9-a03e-2a81ec075e8f", "metadata": {}, "outputs": [], "source": ["supert, superd, superl, supers = faster_supertrend(\n", "    high['BTCUSDT'].values, \n", "    low['BTCUSDT'].values, \n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "1337f05f-b7e5-405a-abc0-7bab2099cabc", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([          nan,           nan,           nan, ..., 47608.3465635,\n", "       47608.3465635, 47608.3465635])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["supert"]}, {"cell_type": "code", "execution_count": 29, "id": "30a8e2dd-c24c-4b25-8854-89ab4f256911", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1,  1,  1, ..., -1, -1, -1])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["superd"]}, {"cell_type": "code", "execution_count": 30, "id": "cd5eb5ad-cf92-47e8-9eba-3b222ce8a3c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([nan, nan, nan, ..., nan, nan, nan])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["superl"]}, {"cell_type": "code", "execution_count": 31, "id": "f87b9091-a8b8-49a5-a48d-d35f21d46461", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([          nan,           nan,           nan, ..., 47608.3465635,\n", "       47608.3465635, 47608.3465635])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["supers"]}, {"cell_type": "code", "execution_count": 32, "id": "08d97bbe-5c1b-44fe-94bb-0ce136de5c1c", "metadata": {}, "outputs": [{"data": {"text/plain": ["Open time\n", "2020-01-01 00:00:00+00:00             NaN\n", "2020-01-01 01:00:00+00:00             NaN\n", "2020-01-01 02:00:00+00:00             NaN\n", "2020-01-01 03:00:00+00:00             NaN\n", "2020-01-01 04:00:00+00:00             NaN\n", "                                 ...     \n", "2021-12-31 19:00:00+00:00    47858.398491\n", "2021-12-31 20:00:00+00:00    47608.346563\n", "2021-12-31 21:00:00+00:00    47608.346563\n", "2021-12-31 22:00:00+00:00    47608.346563\n", "2021-12-31 23:00:00+00:00    47608.346563\n", "Length: 17513, dtype: float64"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.Series(supert, index=close.index)"]}, {"cell_type": "code", "execution_count": 33, "id": "1d9e597d-0cf8-415e-a199-eb55a4a513d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.37 ms ± 8.06 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n"]}], "source": ["%%timeit\n", "faster_supertrend(\n", "    high['BTCUSDT'].values, \n", "    low['BTCUSDT'].values,\n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "markdown", "id": "2e52b679-5838-46ad-9e91-7674021261e0", "metadata": {}, "source": ["### NumPy + Numba + TA-Lib"]}, {"cell_type": "code", "execution_count": 34, "id": "f2de5960-18c0-4b21-a0a8-856738c14bc9", "metadata": {}, "outputs": [], "source": ["import talib\n", "\n", "def faster_supertrend_talib(high, low, close, period=7, multiplier=3):\n", "    avg_price = talib.MEDPRICE(high, low)\n", "    atr = talib.ATR(high, low, close, period)\n", "    upper, lower = get_basic_bands(avg_price, atr, multiplier)\n", "    return get_final_bands_nb(close, upper, lower)"]}, {"cell_type": "code", "execution_count": 35, "id": "4ee2ed2c-2feb-4ef4-a8b7-914cd837610d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([          nan,           nan,           nan, ..., 47608.3465635,\n", "        47608.3465635, 47608.3465635]),\n", " array([ 1,  1,  1, ..., -1, -1, -1]),\n", " array([nan, nan, nan, ..., nan, nan, nan]),\n", " array([          nan,           nan,           nan, ..., 47608.3465635,\n", "        47608.3465635, 47608.3465635]))"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["faster_supertrend_talib(\n", "    high['BTCUSDT'].values, \n", "    low['BTCUSDT'].values, \n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "code", "execution_count": 36, "id": "92ff0625-ad73-44d7-8984-8103a702a03b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["441 µs ± 2.04 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n"]}], "source": ["%%timeit\n", "faster_supertrend_talib(\n", "    high['BTCUSDT'].values, \n", "    low['BTCUSDT'].values, \n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "markdown", "id": "9f2d1d3c-80f6-4bdf-972b-af349ec3e1ea", "metadata": {}, "source": ["## Indicator factory"]}, {"cell_type": "code", "execution_count": 37, "id": "fcd4ca20-aa82-42f9-916b-7b9024890b14", "metadata": {}, "outputs": [], "source": ["SuperTrend = vbt.IF(\n", "    class_name='SuperTrend',\n", "    short_name='st',\n", "    input_names=['high', 'low', 'close'],\n", "    param_names=['period', 'multiplier'],\n", "    output_names=['supert', 'superd', 'superl', 'supers']\n", ").with_apply_func(\n", "    faster_supertrend_talib, \n", "    takes_1d=True,\n", "    period=7, \n", "    multiplier=3\n", ")"]}, {"cell_type": "code", "execution_count": 38, "id": "c9b84719-41b9-4622-87a4-1afc4da1cafd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on method run:\n", "\n", "run(high, low, close, period=Default(value=7), multiplier=Default(value=3), short_name='st', hide_params=None, hide_default=True, **kwargs) method of vectorbtpro.generic.analyzable.MetaAnalyzable instance\n", "    Run `SuperTrend` indicator.\n", "    \n", "    * Inputs: `high`, `low`, `close`\n", "    * Parameters: `period`, `multiplier`\n", "    * Outputs: `supert`, `superd`, `superl`, `supers`\n", "    \n", "    Pass a list of parameter names as `hide_params` to hide their column levels, or True to hide all.\n", "    Set `hide_default` to False to show the column levels of the parameters with a default value.\n", "    \n", "    Other keyword arguments are passed to `vectorbtpro.indicators.factory.run_pipeline`.\n", "\n"]}], "source": ["help(SuperTrend.run)"]}, {"cell_type": "code", "execution_count": 39, "id": "98812dfa-3e5b-487f-9bb0-738ff2e9ffdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["symbol                          BTCUSDT      ETHUSDT\n", "Open time                                           \n", "2020-01-01 00:00:00+00:00           NaN          NaN\n", "2020-01-01 01:00:00+00:00           NaN          NaN\n", "2020-01-01 02:00:00+00:00           NaN          NaN\n", "2020-01-01 03:00:00+00:00           NaN          NaN\n", "2020-01-01 04:00:00+00:00           NaN          NaN\n", "...                                 ...          ...\n", "2021-12-31 19:00:00+00:00  47858.398491  3792.049621\n", "2021-12-31 20:00:00+00:00  47608.346563  3770.258246\n", "2021-12-31 21:00:00+00:00  47608.346563  3770.258246\n", "2021-12-31 22:00:00+00:00  47608.346563  3770.258246\n", "2021-12-31 23:00:00+00:00  47608.346563  3770.258246\n", "\n", "[17513 rows x 2 columns]\n"]}], "source": ["st = SuperTrend.run(high, low, close)\n", "print(st.supert)"]}, {"cell_type": "code", "execution_count": 40, "id": "200ef4a9-cd6e-4f04-a279-0e4941933a8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.09 ms ± 305 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%%timeit\n", "SuperTrend.run(high, low, close)"]}, {"cell_type": "markdown", "id": "a038fda3-4789-4756-a298-f57a48cd2e99", "metadata": {}, "source": ["### Using expressions"]}, {"cell_type": "code", "execution_count": 41, "id": "ffcfb664-d323-49d2-8544-9696dd74a316", "metadata": {}, "outputs": [], "source": ["expr = \"\"\"\n", "SuperTrend[st]:\n", "medprice = @talib_medprice(high, low)\n", "atr = @talib_atr(high, low, close, @p_period)\n", "upper, lower = get_basic_bands(medprice, atr, @p_multiplier)\n", "supert, superd, superl, supers = get_final_bands(close, upper, lower)\n", "supert, superd, superl, supers\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 42, "id": "522edc62-b6ad-4f1e-95f2-d8b871609b09", "metadata": {}, "outputs": [], "source": ["SuperTrend = vbt.IF.from_expr(\n", "    expr, \n", "    takes_1d=True,\n", "    get_basic_bands=get_basic_bands,\n", "    get_final_bands=get_final_bands_nb,\n", "    period=7, \n", "    multiplier=3\n", ")"]}, {"cell_type": "code", "execution_count": 43, "id": "dcd6acd6-ee0e-4953-9bb0-223fbb0158f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["symbol                          BTCUSDT      ETHUSDT\n", "Open time                                           \n", "2020-01-01 00:00:00+00:00           NaN          NaN\n", "2020-01-01 01:00:00+00:00           NaN          NaN\n", "2020-01-01 02:00:00+00:00           NaN          NaN\n", "2020-01-01 03:00:00+00:00           NaN          NaN\n", "2020-01-01 04:00:00+00:00           NaN          NaN\n", "...                                 ...          ...\n", "2021-12-31 19:00:00+00:00  47858.398491  3792.049621\n", "2021-12-31 20:00:00+00:00  47608.346563  3770.258246\n", "2021-12-31 21:00:00+00:00  47608.346563  3770.258246\n", "2021-12-31 22:00:00+00:00  47608.346563  3770.258246\n", "2021-12-31 23:00:00+00:00  47608.346563  3770.258246\n", "\n", "[17513 rows x 2 columns]\n"]}], "source": ["st = SuperTrend.run(high, low, close)\n", "print(st.supert)"]}, {"cell_type": "code", "execution_count": 44, "id": "3fcb34c2-d1cc-48e7-8eb4-fdd0a1a9b1bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7.19 ms ± 222 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%%timeit\n", "SuperTrend.run(high, low, close)"]}, {"cell_type": "markdown", "id": "5b834219-4c05-478a-b581-9bb6ec0db8fb", "metadata": {}, "source": ["## Plot indicator"]}, {"cell_type": "code", "execution_count": 45, "id": "21bb38bf-b6da-42b1-ab48-039d9e75a16d", "metadata": {}, "outputs": [], "source": ["class SuperTrend(SuperTrend):\n", "    def plot(self, \n", "             column=None, \n", "             close_kwargs=None,\n", "             superl_kwargs=None,\n", "             supers_kwargs=None,\n", "             fig=None, \n", "             **layout_kwargs):\n", "        close_kwargs = close_kwargs if close_kwargs else {}\n", "        superl_kwargs = superl_kwargs if superl_kwargs else {}\n", "        supers_kwargs = supers_kwargs if supers_kwargs else {}\n", "        \n", "        close = self.select_col_from_obj(self.close, column).rename('Close')\n", "        supers = self.select_col_from_obj(self.supers, column).rename('Short')\n", "        superl = self.select_col_from_obj(self.superl, column).rename('Long')\n", "        \n", "        fig = close.vbt.plot(fig=fig, **close_kwargs, **layout_kwargs)\n", "        supers.vbt.plot(fig=fig, **supers_kwargs)\n", "        superl.vbt.plot(fig=fig, **superl_kwargs)\n", "        \n", "        return fig"]}, {"cell_type": "code", "execution_count": 46, "id": "99b0be24-8256-468a-bdaf-bd82467e4819", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(33, 34, 44); fill-opacity: 1;\"/><defs id=\"defs-ff1a8a\"><g class=\"clips\"><clipPath id=\"clipff1a8axyplot\" class=\"plotclip\"><rect width=\"626\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipff1a8ax\"><rect x=\"44\" y=\"0\" width=\"626\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipff1a8ay\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipff1a8axy\"><rect x=\"44\" y=\"46\" width=\"626\" height=\"261\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"minor-gridlayer\"><g class=\"x\"/><g class=\"y\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(122.35,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(259.47,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(396.58,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(533.7,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,280.4)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,237.43)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,194.45)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,151.48000000000002)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,108.5)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,65.53)\" d=\"M44,0h626\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(44,46)\" clip-path=\"url(#clipff1a8axyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter tracea788cd97-a503-4b63-b0a6-d630f38292d5\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,219.19L1.63,213.53L2.45,215.06L3.26,215.73L4.08,215.13L4.9,215.01L5.71,216.37L7.35,218.18L8.16,217.04L8.98,217.45L9.79,215.01L11.43,215.37L12.24,214.27L13.06,213.31L13.87,214.05L14.69,213.72L15.51,214.68L17.14,214.36L17.96,217.38L19.59,216.26L20.4,217.99L22.04,223.26L22.85,222.18L24.49,222.71L25.3,224.9L26.93,220.48L27.75,220.49L28.57,222.39L29.38,220.8L30.2,223.13L31.01,222.75L31.83,223.14L32.65,229.9L33.46,236.98L34.28,236.63L35.1,235.84L35.91,238.83L36.73,236.31L37.54,236.57L38.36,237.35L39.18,239.82L39.99,243.95L40.81,238.11L41.62,238.52L42.44,216.34L43.26,215.45L44.07,217.02L44.89,216.94L45.71,212L46.52,204.45L47.34,207.56L48.15,205.14L48.97,206.66L49.79,204.42L50.6,212.51L52.23,201.22L53.05,203.87L55.5,207.16L56.32,210.08L57.13,208.57L57.95,204.75L58.76,208.42L59.58,207.83L61.21,205.77L62.03,205.62L63.66,203.89L64.48,204.48L65.29,204.61L66.11,203.13L66.93,207.04L67.74,205.48L69.37,208.28L70.19,207.18L71.01,206.98L71.82,208.01L72.64,204.15L73.46,208.02L75.09,205.09L75.9,204.26L77.54,203.97L78.35,202.3L79.17,194.93L79.98,196.26L80.8,193.49L81.62,195.11L84.07,194.55L84.88,197.44L85.7,197.64L86.51,198.72L87.33,198.1L88.15,195.12L88.96,197.34L89.78,197.29L90.59,194.14L91.41,194.75L92.23,194.46L93.04,196.69L93.86,196.23L94.68,197.31L96.31,203.83L97.12,203.57L97.94,201.85L98.76,198L99.57,187.01L100.39,187.91L101.2,188.89L102.02,188.6L103.65,190.75L104.47,187.34L106.1,186.16L106.92,187.58L107.73,186.49L108.55,188.96L109.37,187.31L110.18,189.71L111,188.91L111.81,186.4L112.63,188.59L113.45,188L114.26,187.02L115.08,184.69L117.53,158.04L118.34,156.56L119.98,157.49L120.79,162.13L122.43,159.38L123.24,158.48L124.06,158.42L124.87,160.81L126.51,157.46L127.32,159.71L128.95,162.31L129.77,168.69L131.4,153.75L132.22,145.21L133.04,142.12L133.85,134.47L134.67,147.04L135.48,144.19L136.3,135.97L137.12,111.34L138.75,124.51L139.56,122.64L140.38,118.83L141.2,119.68L142.01,119.3L142.83,122.9L143.65,121.63L144.46,122.91L146.09,119.88L146.91,123.86L147.73,115.36L148.54,121.72L149.36,122.15L150.17,138.57L151.81,143.84L152.62,151.6L153.44,144.5L154.26,147.07L155.07,141.91L155.89,143.64L156.7,152.19L157.52,148.04L159.15,152.34L159.97,151.3L161.6,156.32L162.42,152.76L163.23,154.2L164.05,159.42L164.87,157.36L165.68,158.89L166.5,156.97L167.31,157.26L168.13,158.29L168.95,156.44L170.58,165.76L171.4,164.73L172.21,155.54L173.03,164.07L173.84,166.5L174.66,164.67L176.29,163.92L177.11,165.21L177.92,165.33L178.74,164.09L179.56,166.05L180.37,170.04L182.01,169.68L182.82,173.68L183.64,172.82L184.45,168.06L186.09,157.56L186.9,159.59L188.53,142.73L189.35,152.52L190.17,151.35L190.98,146.55L192.62,142.16L193.43,144.16L194.25,140.71L195.06,131.52L195.88,133.93L196.7,130.84L197.51,130.73L198.33,134.83L199.96,141.28L200.78,137.71L202.41,143.9L203.23,138.63L204.86,144.5L205.67,143.13L206.49,140.71L207.31,143.83L208.94,133.89L209.75,134.65L210.57,133.91L211.39,135.41L212.2,134.54L213.02,139.43L213.84,141.36L214.65,146.73L216.28,142.82L217.1,140.29L217.92,140.6L218.73,142.11L219.55,139.34L220.37,141.25L221.18,140.07L222,136.37L222.81,136.63L223.63,137.96L224.45,138.65L225.26,137.09L226.08,134.27L226.89,136.66L227.71,135.33L228.53,139.69L229.34,139.73L230.16,138.2L231.79,136.53L232.61,139.66L234.24,132.55L235.06,135.33L235.87,135.05L236.69,138.73L237.5,139.36L238.32,138.28L239.95,143.03L240.77,141.98L241.59,137.28L242.4,138.68L243.22,137.65L244.03,142L244.85,140.47L245.67,140.99L247.3,141.16L248.11,139L248.93,140.01L249.75,139.18L250.56,138.84L251.38,134.97L253.83,138.97L254.64,125.25L255.46,111.34L256.28,111.4L257.09,108.76L257.91,103.41L258.72,107.42L259.54,104.66L261.99,105.07L262.81,103.92L263.62,103.17L264.44,105.48L266.07,89.15L266.89,90.4L267.7,90.26L268.52,81.98L270.15,82.48L270.97,89.3L271.78,85.29L272.6,85.2L273.42,78.83L274.23,82.03L275.05,81.58L275.86,78.5L277.5,97.1L278.31,95.83L279.13,89.96L279.95,94.55L280.76,93.59L281.58,83.56L282.39,79.78L283.21,85.71L284.03,82.73L284.84,71.96L285.66,74.87L286.47,82.9L288.11,79.95L288.92,84.26L289.74,83L290.56,78.47L292.19,74.54L293,77.85L295.45,92.59L296.27,93.44L297.08,92.43L297.9,88.24L298.72,86.46L299.53,90.01L300.35,90.4L301.17,92.98L301.98,91.9L302.8,86.15L303.61,86.57L304.43,88.69L305.25,89.48L306.06,87.43L306.88,91.71L307.69,90.17L308.51,89.65L309.33,87.86L310.14,84.51L310.96,89.05L311.78,86.54L312.59,86.57L313.41,89.12L314.22,84.98L315.86,80.86L316.67,78.38L317.49,71.85L318.31,74.3L319.12,68.18L319.94,68.62L320.75,65.17L321.57,67.92L322.39,70.8L323.2,78.7L324.02,75.31L324.83,81.52L326.47,73.16L327.28,71.87L328.1,69.37L328.92,72.84L329.73,72.85L330.55,69.18L331.36,67.7L332.18,69.96L333,64.31L333.81,67.55L334.63,69.28L335.44,68.86L336.26,75.93L337.08,75.81L337.89,74.13L338.71,75.94L341.16,72.21L341.97,72.84L343.61,69.38L344.42,69.16L346.05,72.54L346.87,70.67L347.69,72.23L348.5,70.42L349.32,70.45L350.14,69.36L350.95,66.43L351.77,69.73L352.58,64.84L353.4,49.95L355.03,48.96L355.85,49.7L356.66,51.23L357.48,56.22L359.11,53.64L359.93,57.26L360.75,59.18L361.56,89.88L362.38,94.47L363.19,94.39L364.01,95.41L364.83,94.14L366.46,91.83L367.28,92.51L368.91,92.79L369.72,86.39L370.54,93.15L371.36,88.14L372.99,92.54L373.8,91.88L374.62,91.08L375.44,93.32L377.07,89.66L377.89,91.28L378.7,93.38L379.52,91.65L380.33,92.27L381.15,95.57L381.97,98.34L382.78,96.31L384.41,90.4L385.23,90.99L386.05,93.29L386.86,91.31L389.31,88.91L390.13,92.17L390.94,93.24L391.76,93.06L394.21,90.51L395.02,90.79L397.47,93.9L398.29,91.85L399.11,94.29L399.92,91.79L401.55,91.64L402.37,91.52L404.82,93.06L405.63,92.71L406.45,93.96L407.27,102.74L408.08,86.68L408.9,85.71L410.53,85.19L411.35,84.94L413.8,86.93L414.61,84.85L415.43,86.64L416.25,83.64L417.06,84.83L417.88,90.12L418.69,89.47L419.51,91.44L421.14,91.34L421.96,93.02L423.59,92.12L424.41,89.45L425.22,88.92L426.04,90.9L427.67,91.18L428.49,91.64L429.3,91.96L430.12,89.8L430.94,91.5L431.75,96.89L432.57,95.97L433.38,97.12L434.2,97.66L435.02,100.07L435.83,102.46L436.65,98.45L438.28,110.56L439.1,110.2L440.73,112.31L441.55,118.37L442.36,117.25L443.18,117.56L443.99,117.97L444.81,120.45L445.63,119.28L446.44,116.57L447.26,116.45L448.08,114.48L450.52,113.3L451.34,117.51L452.16,122.6L452.97,120.9L453.79,122.09L454.6,120.79L455.42,118.74L456.24,119.23L457.05,119.87L457.87,126.09L458.69,127.35L459.5,112.97L462.77,104.92L463.58,108.99L466.03,105.58L466.85,106.63L467.66,104.67L468.48,108.31L469.3,110.72L470.11,121.85L470.93,123.85L471.74,120.5L472.56,122.87L473.38,121.09L475.01,120.09L475.83,121.27L476.64,117.3L477.46,118.49L479.91,123.33L480.72,120.58L481.54,121.23L482.35,119.65L483.17,118.4L483.99,119.24L484.8,119.14L485.62,117.26L486.44,116.8L487.25,114.85L489.7,120.44L490.52,118.9L491.33,120.55L492.15,116.75L492.96,116.89L493.78,116.03L494.6,117.97L495.41,115.35L497.05,112.36L497.86,110.07L498.68,107.27L499.49,109.15L501.13,109.39L501.94,108.94L503.57,98.7L504.39,98.6L506.02,99.61L506.84,96.81L507.66,98.8L508.47,95.59L509.29,91.1L510.1,91.61L510.92,92.11L511.74,90.86L512.55,92.79L513.37,92.3L514.19,91.24L515,91.9L515.82,96.04L516.63,93.27L517.45,93.95L518.27,85.22L519.08,84.29L519.9,84.88L522.35,80.42L523.16,82.52L524.8,68.34L525.61,63.65L526.43,65.52L527.24,70.89L528.06,70.45L528.88,56.55L529.69,51.08L530.51,52.21L531.32,52.88L532.14,57.78L532.96,57.42L533.77,61.1L534.59,59.07L535.41,63.49L536.22,64.64L537.04,60.86L537.85,55.44L538.67,58.28L539.49,55.08L540.3,59.59L541.12,60.39L541.93,65.15L544.38,61.57L545.2,57.22L546.02,58.15L546.83,51.07L547.65,30.34L548.46,34.04L549.28,32.92L550.1,34.73L550.91,32.98L551.73,33.81L552.54,31.99L553.36,33.9L554.99,29.23L555.81,33.93L556.63,27.44L557.44,36.45L558.26,38.43L559.07,34.65L559.89,34.22L560.71,38.01L561.52,31.46L562.34,32.95L563.97,28.64L564.79,29.29L565.6,30.02L566.42,33.11L568.05,38.58L568.87,37.03L569.68,40.84L570.5,38.41L571.32,34.18L572.13,35.17L572.95,34.54L573.77,28.9L576.21,33.77L577.03,33.43L578.66,29.24L579.48,32.44L580.29,29.25L581.11,30.62L582.74,19.59L583.56,18.94L584.38,13.05L585.19,13.85L586.01,14.05L586.82,18.39L587.64,21.77L588.46,21.38L589.27,24.31L590.09,24.34L590.9,26.74L591.72,32.81L592.54,29.9L593.35,30.31L594.17,29.99L594.99,31.6L596.62,34.28L597.43,38.24L599.07,39.88L599.88,33.35L601.51,38.22L602.33,35.11L603.15,36.49L603.96,34.91L605.6,27.92L606.41,32.17L608.86,24.52L609.68,25.34L611.31,29.75L612.13,28.29L612.94,26.15L613.76,29.45L614.57,29.17L615.39,29.88L617.02,35.68L617.84,32.82L618.65,32.41L619.47,33.8L620.29,27.63L621.1,29.72L621.92,28.96L622.74,30.95L623.55,29.72L624.37,29.84L625.18,30.03L626,29.45\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace785cdfd9-3c53-4dac-aad1-5ccf362d65ff\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M22.04,213.85L24.49,214.66L25.3,215.58L32.65,215.58L33.46,220.12L35.1,222.6L35.91,224.33L37.54,225.35L38.36,226.8L40.81,229.8L41.62,229.8\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M150.99,107.63L153.44,117.32L154.26,117.32L155.89,117.32L156.7,118.21L158.34,121.02L159.15,126.08L161.6,134.27L162.42,134.27L163.23,134.99L164.05,138.13L164.87,138.25L165.68,140.56L168.95,141.13L169.76,142.7L171.4,148.09L172.21,148.09L178.74,148.3L179.56,149.2L182.01,154.78L182.82,156.4L186.9,156.4\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M361.56,48.64L363.19,65.93L364.01,65.93L364.83,68.88L365.64,68.88L367.28,71.99L368.09,74.15L369.72,75.6L370.54,75.6L379.52,76.35L380.33,77.38L382.78,81.15L383.6,81.15L396.66,81.85L397.47,83L404.82,83.67L405.63,84.25L408.08,86.36\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M431.75,82.33L432.57,85.21L433.38,85.27L434.2,86.1L435.02,88.6L437.47,88.63L438.28,89.7L439.1,93.29L439.91,93.29L440.73,93.29L441.55,95.91L442.36,98.24L443.18,98.24L444.81,100.95L445.63,102.85L451.34,102.85L452.16,104.71L453.79,104.86L454.6,105.57L456.24,106.27L457.05,107.09L458.69,110.04L459.5,110.04L460.32,110.04\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M470.11,96.9L470.93,103.98L471.74,103.98L472.56,103.98L473.38,105.15L478.27,105.66L479.09,106.66L480.72,107.9L481.54,107.9L483.99,107.9L484.8,109.01L497.86,109.37\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M597.43,18.66L598.25,21.38L599.07,21.38L626,21.38\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace807c6f5b-d2d3-4f73-8b9c-7adda06cb76b\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M5.71,222.81L11.43,222.78L12.24,221.77L15.51,221.2L16.32,220.84L17.96,220.4L18.77,220.4L21.22,220.4\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M42.44,247.95L44.07,237.08L44.89,235.79L47.34,227.63L48.15,226.07L50.6,223.17L51.42,223.17L60.4,222.57L61.21,219.78L62.03,219.08L62.84,217.05L64.48,215.71L65.29,215.59L66.93,214.53L67.74,214.53L77.54,214.53L78.35,213.35L80.8,207.88L81.62,206.99L89.78,206.47L90.59,205.45L92.23,204.12L93.04,204.12L98.76,204.12L99.57,207.11L101.2,203.76L102.02,203.76L105.29,200.4L106.1,198.36L114.26,198.33L115.08,196.31L115.9,193.86L116.71,188.5L119.16,176.98L119.98,176.36L122.43,176.36L123.24,175.2L124.87,173.44L125.69,172.78L126.51,169.6L127.32,169.6L132.22,169.6L133.04,168.07L133.85,166.2L134.67,166.2L136.3,166.2L137.12,163.03L137.93,153.94L138.75,153.94L140.38,153.94L141.2,151.21L142.01,148.33L142.83,148.33L143.65,148.33L144.46,146.9L146.09,140.33L146.91,140.33L150.17,140.33\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M187.72,177.33L188.53,170.87L189.35,170.87L194.25,170.49L195.06,166.99L196.7,158.2L197.51,158.2L208.94,157.65L209.75,152.24L211.39,152.24L212.2,151.24L224.45,151.24L225.26,150.39L227.71,148.05L228.53,148.05L250.56,147.81L251.38,146.83L253.01,146.3L253.83,146.3L254.64,146.5L255.46,137.84L257.09,133.35L257.91,126.48L259.54,126.48L260.36,123.95L261.99,122.34L262.81,122.34L264.44,120.51L265.25,120.51L266.89,113.65L267.7,113.65L271.78,113.65L272.6,112.7L273.42,110.36L274.23,110.36L282.39,109.9L283.21,109.5L284.03,109.04L284.84,103.94L291.37,103.94L292.19,101.46L294.64,101.17L295.45,101.17L315.04,100.47L315.86,97.4L316.67,97.4L317.49,94.32L318.31,91.41L319.12,91.41L321.57,88.36L322.39,88.36L341.97,87.87L342.79,85.87L344.42,82.5L345.24,82.5L350.14,82.34L350.95,80.01L352.58,80.01L353.4,75.36L355.03,65.77L355.85,65.77L360.75,65.77\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M408.9,102.32L409.72,102.32L410.53,101.3L412.16,100.68L412.98,100.68L415.43,97.25L416.25,96.28L430.94,96.28\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M461.13,129.47L462.77,126.45L463.58,124.96L464.4,124.96L465.22,123.02L466.85,121.2L467.66,120.73L469.3,120.73\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M498.68,122.2L501.13,121.15L501.94,119.87L502.76,118.73L503.57,115.8L504.39,111.87L505.21,111.87L508.47,110.79L509.29,106.53L510.92,104.84L511.74,104.71L513.37,104.71L514.19,103.86L515.82,102.4L516.63,102.4L519.08,101.84L519.9,99.35L520.71,99.35L521.53,96.51L523.16,95.66L523.98,94.69L525.61,85.62L526.43,84.32L528.88,84.32L529.69,75.61L530.51,73.41L531.32,73.41L546.83,73.41L547.65,71.37L549.28,60.22L550.1,60.22L550.91,59.66L551.73,57.37L553.36,53.04L554.18,53.04L554.99,50.4L555.81,50.4L563.97,50.4L564.79,49.04L573.77,48.33L574.58,46.15L581.93,45.42L582.74,44.98L583.56,43.94L584.38,40.53L586.01,35.91L586.82,35.91L596.62,35.91\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(122.35,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 5</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(259.47,0)\">Jan 12</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(396.58,0)\">Jan 19</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(533.7,0)\">Jan 26</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" transform=\"translate(0,280.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">7000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,237.43)\">7500</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,194.45)\">8000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,151.48000000000002)\">8500</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,108.5)\">9000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"43\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,65.53)\">9500</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"smithlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-ff1a8a\"><g class=\"clips\"/><clipPath id=\"legendff1a8a\"><rect width=\"230\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(440,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"230\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(33, 34, 44); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legendff1a8a)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.859375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(77.359375,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Short</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"75.4375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(155.296875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Long</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(50, 205, 50); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"71.546875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["st = SuperTrend.run(high, low, close)\n", "st.loc[date_range, 'BTCUSDT'].plot(\n", "    superl_kwargs=dict(trace_kwargs=dict(line_color='limegreen')),\n", "    supers_kwargs=dict(trace_kwargs=dict(line_color='red'))\n", ").show_svg()"]}, {"cell_type": "markdown", "id": "2d7956a9-a24c-4db1-871e-ef5f938e193e", "metadata": {}, "source": ["## Test indicator"]}, {"cell_type": "code", "execution_count": 47, "id": "df6dfa41-4206-4d61-a270-6d5f7fc77c2b", "metadata": {}, "outputs": [], "source": ["entries = (~st.superl.isnull()).vbt.signals.fshift()\n", "exits = (~st.supers.isnull()).vbt.signals.fshift()"]}, {"cell_type": "code", "execution_count": 48, "id": "1cd2f65d-2682-4cfd-bbcf-54702bc115b2", "metadata": {}, "outputs": [], "source": ["pf = vbt.Portfolio.from_signals(\n", "    close=close, \n", "    entries=entries, \n", "    exits=exits, \n", "    fees=0.001, \n", "    freq='1h'\n", ")"]}, {"cell_type": "code", "execution_count": 49, "id": "757609ba-f3ed-4c70-8e3c-9c6f0cdca05c", "metadata": {}, "outputs": [{"data": {"text/plain": ["Start                         2020-01-01 00:00:00+00:00\n", "End                           2021-12-31 23:00:00+00:00\n", "Period                                729 days 17:00:00\n", "Start Value                                       100.0\n", "Min Value                                     98.469385\n", "Max Value                                   1805.987865\n", "End Value                                   1135.272383\n", "Total Return [%]                            1035.272383\n", "Benchmark Return [%]                        2752.665477\n", "Total Time Exposure [%]                       51.750128\n", "Max Gross Exposure [%]                            100.0\n", "Max Drawdown [%]                               37.39953\n", "Max Drawdown Duration                  85 days 09:00:00\n", "Total Orders                                        348\n", "Total Fees Paid                              272.755758\n", "Total Trades                                        174\n", "Win Rate [%]                                  43.103448\n", "Best Trade [%]                                33.286985\n", "Worst Trade [%]                              -13.783496\n", "Avg Winning Trade [%]                          7.815551\n", "Avg Losing Trade [%]                          -3.021041\n", "Avg Winning Trade Duration              3 days 06:43:12\n", "Avg Losing Trade Duration     1 days 07:54:32.727272727\n", "Profit Factor                                  1.390947\n", "Expectancy                                     5.949841\n", "Sharpe Ratio                                   2.258501\n", "Calmar Ratio                                   6.320363\n", "Omega Ratio                                    1.103525\n", "<PERSON><PERSON><PERSON>                                   3.27869\n", "Name: ETHUSDT, dtype: object"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["pf['ETHUSDT'].stats()"]}, {"cell_type": "markdown", "id": "961fa4a3-74c3-4b90-9678-aaee56cf60af", "metadata": {}, "source": ["### Optimization"]}, {"cell_type": "code", "execution_count": 50, "id": "a1b757f6-0c30-4102-8580-d6d82bf249f8", "metadata": {}, "outputs": [], "source": ["periods = np.arange(4, 20)\n", "multipliers = np.arange(20, 41) / 10"]}, {"cell_type": "code", "execution_count": 51, "id": "65b4d9ed-2866-4044-8023-829d5e9b0f30", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c1df42d84b14ccbab5f39c9a06e6bdb", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/672 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["st = SuperTrend.run(\n", "    high, low, close, \n", "    period=periods, \n", "    multiplier=multipliers,\n", "    param_product=True,\n", ")"]}, {"cell_type": "code", "execution_count": 52, "id": "00adab7e-1ac1-4aca-a2b5-e00926381009", "metadata": {}, "outputs": [{"data": {"text/plain": ["MultiIndex([( 4, 2.0, 'BTCUSDT'),\n", "            ( 4, 2.0, 'ETHUSDT'),\n", "            ( 4, 2.1, 'BTCUSDT'),\n", "            ( 4, 2.1, 'ETHUSDT'),\n", "            ( 4, 2.2, 'BTCUSDT'),\n", "            ( 4, 2.2, 'ETHUSDT'),\n", "            ( 4, 2.3, 'BTCUSDT'),\n", "            ( 4, 2.3, 'ETHUSDT'),\n", "            ( 4, 2.4, 'BTCUSDT'),\n", "            ( 4, 2.4, 'ETHUSDT'),\n", "            ...\n", "            (19, 3.6, 'BTCUSDT'),\n", "            (19, 3.6, 'ETHUSDT'),\n", "            (19, 3.7, 'BTCUSDT'),\n", "            (19, 3.7, 'ETHUSDT'),\n", "            (19, 3.8, 'BTCUSDT'),\n", "            (19, 3.8, 'ETHUSDT'),\n", "            (19, 3.9, 'BTCUSDT'),\n", "            (19, 3.9, 'ETHUSDT'),\n", "            (19, 4.0, 'BTCUSDT'),\n", "            (19, 4.0, 'ETHUSDT')],\n", "           names=['st_period', 'st_multiplier', 'symbol'], length=672)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["st.wrapper.columns"]}, {"cell_type": "code", "execution_count": 53, "id": "bcea7660-bf82-4ea2-ae3d-6407914167bc", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(33, 34, 44); fill-opacity: 1;\"/><defs id=\"defs-a1783b\"><g class=\"clips\"><clipPath id=\"clipa1783bxyplot\" class=\"plotclip\"><rect width=\"634\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa1783bx\"><rect x=\"36\" y=\"0\" width=\"634\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa1783by\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa1783bxy\"><rect x=\"36\" y=\"46\" width=\"634\" height=\"261\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"minor-gridlayer\"><g class=\"x\"/><g class=\"y\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(115.35,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(254.22,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(393.09,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(531.96,0)\" d=\"M0,46v261\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,278.61)\" d=\"M36,0h634\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,239.86)\" d=\"M36,0h634\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,201.11)\" d=\"M36,0h634\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,162.36)\" d=\"M36,0h634\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,123.61)\" d=\"M36,0h634\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,84.86)\" d=\"M36,0h634\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(36,46)\" clip-path=\"url(#clipa1783bxyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace7154262e-8b08-4fc3-b369-8b0a2fdbb3f5\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,236.98L1.65,229.31L2.48,231.83L4.13,231.44L4.96,230.9L5.79,231.68L6.61,231.21L7.44,231.95L8.27,229.89L9.09,229.74L9.92,226.37L10.75,226.1L11.57,225.01L12.4,224.55L13.23,221.56L14.88,224.7L15.71,225.4L16.53,224.16L17.36,225.71L18.19,231.56L19.01,229.62L19.84,230.01L20.66,232.02L22.32,236.09L23.14,234.35L23.97,234.04L24.8,234.85L25.62,235.47L26.45,231.79L28.93,234.43L29.76,232.76L30.58,235.12L31.41,235.05L32.24,234.19L33.06,241.91L33.89,242.68L34.72,242.33L35.54,241.83L36.37,243.61L37.2,241.44L38.02,242.72L39.68,243.96L40.5,247.95L41.33,244.54L42.16,244.66L42.98,235.32L43.81,235.67L44.64,235.82L45.46,237.02L46.29,224.27L47.12,223.5L47.94,224.7L48.77,223.07L49.6,223.96L50.42,218.89L51.25,226.1L52.08,222.8L53.73,217.22L54.56,219.04L56.21,219.24L57.04,222.92L57.86,220.83L58.69,215.75L60.34,220.94L61.17,219.16L61.99,219.66L62.82,218.38L63.65,218.42L64.47,217.3L65.3,217.45L66.13,218.66L66.95,216.6L67.78,219.43L68.61,218L69.43,221.79L71.91,219.97L72.74,220.63L73.57,214.78L74.39,219L76.87,216.64L77.7,216.72L78.53,216.33L79.35,214.16L80.18,210.71L81.01,211.45L81.83,208.74L82.66,209.7L83.49,208.62L84.31,208.93L85.14,209.28L85.97,211.76L86.79,211.99L87.62,213.7L88.45,212.96L89.27,209.7L90.93,210.98L91.75,204.47L92.58,204.86L93.41,202.38L95.89,202.19L96.71,211.8L97.54,213.08L98.37,211.8L99.19,210.71L100.02,204.63L101.67,195.83L102.5,196.1L104.98,198.23L105.8,191.34L106.63,190.64L107.46,186.22L108.28,187.89L109.11,184.94L109.94,192.93L110.76,189.55L111.59,193.08L112.42,191.22L113.24,186.84L114.07,189.63L114.9,188.62L115.72,185.45L116.55,187.38L117.38,182.04L118.2,177.77L119.03,179.25L120.68,178.43L121.51,180.29L122.34,184.21L123.16,182.35L124.82,182.73L125.64,181.61L126.47,183.97L127.3,182.7L128.12,183.7L128.95,189.48L130.6,190.76L131.43,195.87L133.08,183.32L133.91,184.44L134.74,181.49L135.56,175.41L137.22,185.37L138.04,183.01L138.87,164.6L139.69,172.12L140.52,176.26L141.35,176.5L142.17,174.36L143,175.26L143.83,174.05L144.65,180.06L145.48,178.86L146.31,181.03L147.13,178.78L147.96,180.29L148.79,184.71L149.61,182L151.27,188.35L152.09,198.47L152.92,198.58L153.75,196.34L154.57,202.23L155.4,197.19L156.23,197.03L157.05,192L157.88,191.07L158.71,194.28L159.53,190.1L160.36,192.31L161.19,194.09L162.01,193.93L162.84,197.11L163.67,196.14L164.49,194.13L165.32,196.37L166.15,200.52L166.97,198.16L167.8,200.68L168.63,199.82L170.28,202.34L171.11,200.83L171.93,209.67L172.76,209.2L173.59,209.63L174.41,203.39L175.24,202.57L176.07,205.52L177.72,202.61L178.54,204.86L180.2,207.26L181.02,205.56L182.68,210.21L183.5,206.95L184.33,206.91L185.16,209.12L185.98,209.28L186.81,206.18L188.46,194.4L189.29,198.58L190.94,184.13L191.77,189.59L194.25,180.6L195.08,181.42L196.73,182.7L197.56,175.1L198.38,180.1L199.21,177.81L203.34,182.66L204.17,182.5L205,183.2L205.82,179.17L207.48,183.66L208.3,183.35L209.13,180.1L209.96,184.01L210.78,180.64L211.61,169.29L213.26,168.13L214.09,169.48L214.92,171.03L215.74,178.9L217.4,184.63L218.22,181.34L219.05,181.15L219.87,177.97L221.53,179.94L222.35,177.97L224.01,176.26L224.83,175.14L225.66,176.77L226.49,180.1L227.31,180.37L228.14,179.63L228.97,173.82L229.79,177.58L230.62,174.79L231.45,178.55L233.1,175.91L233.93,176.57L234.75,175.49L235.58,176.69L237.23,168.51L238.06,174.52L238.89,173.28L239.71,182.54L241.37,178.94L242.19,180.68L243.02,184.48L243.85,182.73L244.67,179.83L245.5,181.03L246.33,178.47L247.15,183.12L247.98,181.61L248.81,182.5L250.46,182.89L251.29,180.49L252.11,182L252.94,180.91L253.77,180.29L254.59,176.73L256.25,178.86L257.07,179.98L258.72,165.41L259.55,165.96L261.2,158.52L262.03,161.15L263.68,158.98L264.51,157.78L266.16,153.25L266.99,149.06L267.82,150.88L268.64,139.3L271.12,119.46L271.95,101.21L272.78,94.62L273.6,95.35L274.43,102.83L275.26,100.9L276.08,101.48L276.91,94.5L277.74,80.44L278.56,88.22L279.39,89.39L280.22,106.13L281.04,114.23L281.87,112.71L282.7,104.07L283.52,110.39L284.35,108.76L285.18,96.09L286,88.3L286.83,99.19L287.66,94.54L288.48,84.39L290.14,107.02L290.96,100.28L291.79,97.18L292.62,102.14L294.27,91.87L295.1,95.74L295.92,90.32L296.75,91.56L298.4,114.5L299.23,115.23L300.05,116.94L300.88,114.85L302.53,107.87L303.36,112.48L305.01,111.75L305.84,106.71L306.67,100.59L307.49,106.36L308.32,109.73L309.15,107.52L309.97,106.59L310.8,108.8L311.63,103.34L312.45,103.38L313.28,102.52L314.11,94.81L314.93,103.22L315.76,100.31L316.59,100.04L317.41,101.71L319.07,94.39L319.89,96.44L320.72,90.55L321.55,77.84L322.37,85.2L323.2,73.42L324.85,69.55L325.68,74.58L326.51,77.65L327.33,85.28L328.16,83.07L328.99,91.98L330.64,77.45L331.47,76.25L332.29,68.42L333.12,80.59L333.95,80.98L334.77,72.49L335.6,70.83L336.43,78.19L337.25,65.17L338.08,67.53L338.9,73.03L339.73,70.13L341.38,85.01L342.21,85.67L343.04,85.09L343.86,83.42L344.69,80.13L345.52,81.44L346.34,81.21L347.17,73.65L348.82,49.55L349.65,56.18L350.48,62.46L351.3,58.66L352.13,62.65L352.96,58.35L353.78,56.33L354.61,51.95L355.44,50.79L356.26,61.56L357.09,53.47L357.92,56.95L358.74,53.08L359.57,53.39L360.4,54.59L361.22,53.89L362.05,56.84L362.88,52.23L363.7,52.73L364.53,56.29L365.36,60.91L366.18,90.7L367.84,99.38L368.66,99.23L370.32,96.13L371.14,96.48L371.97,99.46L372.8,97.29L373.62,98.88L374.45,93.84L376.1,90.05L376.93,94.35L378.58,95.2L379.41,94.42L380.23,97.95L381.06,94.42L381.89,93.77L382.71,94.54L383.54,95.63L384.37,98.34L385.19,97.52L386.02,104.58L386.85,106.48L387.67,100.78L389.33,89.89L390.15,89.7L390.98,91.94L391.81,91.17L392.63,86.64L393.46,87.99L394.29,88.15L395.11,87.33L396.77,91.48L397.59,89L398.42,87.76L399.25,84.19L400.9,88.65L401.73,88.34L402.55,91.05L403.38,88.81L404.21,90.16L405.03,85.32L405.86,85.63L406.69,84.97L407.51,81.95L408.34,84.43L409.17,84.97L409.99,86.71L410.82,86.48L411.65,89L412.47,96.52L413.3,84.12L414.95,80.09L415.78,79.58L416.6,78.73L417.43,81.13L418.26,83.15L419.08,82.68L419.91,79.74L420.74,81.64L421.56,78.19L422.39,80.71L423.22,83.65L424.04,81.52L425.7,85.36L426.52,85.32L427.35,91.44L428.18,89.43L429,87.6L429.83,83.73L430.66,83.96L431.48,85.36L433.14,84.74L433.96,85.71L434.79,87.02L435.62,85.09L436.44,88.61L437.27,97.29L438.1,93.53L438.92,95.28L441.4,101.32L442.23,97.45L443.88,102.87L444.71,102.52L445.54,105.89L446.36,105.58L447.19,110.12L448.02,107.83L448.84,104.15L449.67,107.13L450.5,109.61L451.32,117.13L454.63,104.73L455.46,105.47L456.28,104.46L457.11,111.09L457.93,121.67L458.76,120.27L459.59,122.91L460.41,118.64L461.24,118.14L462.07,119.26L462.89,120.66L463.72,131.31L464.55,130.5L465.37,113.92L467.03,109.65L467.85,108.45L468.68,106.2L469.51,112.79L470.33,111.78L471.16,109.5L471.99,101.98L472.81,103.38L474.47,104.46L475.29,106.51L476.12,121.67L476.95,122.21L477.77,119.3L478.6,120.04L480.25,117.79L481.08,118.6L481.91,118.18L482.73,111.63L485.21,119.11L486.04,120.89L486.87,116.12L487.69,116.32L489.35,112.33L490.17,114.85L492.65,111.75L493.48,110.23L495.96,115.89L496.78,115.27L497.61,117.75L498.44,111.63L499.26,113.02L500.09,112.48L500.92,113.22L501.74,111.59L502.57,107.02L503.4,107.17L505.88,102.64L506.7,103.53L508.36,103.3L509.18,97.99L510.01,91.4L510.84,91.71L511.66,92.41L512.49,90.94L513.32,88.38L514.14,89.08L515.8,83.57L516.62,84.35L517.45,86.6L518.28,86.52L519.1,89.27L519.93,88.84L520.76,87.68L521.58,87.99L522.41,93.65L523.24,91.17L524.06,93.34L524.89,83.03L525.72,79.35L526.54,81.09L527.37,77.34L528.2,77.49L529.02,76.44L529.85,78.77L530.68,72.69L531.5,72.96L532.33,69.97L533.16,71.45L534.81,77.3L535.63,72.03L536.46,64.39L537.29,64.97L538.11,65.52L538.94,68.19L540.59,71.64L541.42,69.86L543.07,74.89L543.9,70.28L544.73,70.05L545.55,71.87L547.21,69.31L548.03,68.5L548.86,73.34L549.69,73.62L550.51,73.85L551.34,72.84L552.17,68.19L552.99,70.05L555.47,55.13L556.3,50.91L557.13,55.17L557.95,52.54L558.78,53.08L559.61,51.26L562.09,45.37L562.91,52.85L563.74,47.58L564.57,53.7L565.39,55.95L566.22,55.02L567.05,52.69L567.87,57.88L568.7,51.3L569.53,53.97L571.18,51.92L572.01,52.42L573.66,54.82L574.49,63.19L575.31,65.9L576.14,62.69L576.96,68.5L577.79,64.28L578.62,61.14L579.44,62.69L580.27,62.07L581.1,54.01L582.75,55.02L583.58,59.05L584.4,57.19L585.23,52.77L586.06,50.17L586.88,55.4L587.71,48.04L588.54,47.65L590.19,34.63L591.02,33.04L592.67,18.86L593.5,13.05L595.15,23.05L595.98,23.4L596.8,26.46L597.63,25.6L599.28,33.97L600.11,26.38L600.94,29.25L601.76,28.2L604.24,35.37L605.07,46.57L605.9,45.72L606.72,46.88L607.55,38.7L608.38,44.75L609.2,44.94L610.03,40.91L610.86,41.14L611.68,36.92L612.51,35.18L613.34,31.57L614.16,38.9L614.99,34.63L615.81,24.29L616.64,23.82L617.47,25.26L618.29,31.3L619.12,33.01L619.95,30.84L620.77,26.73L621.6,34.25L623.25,33.74L624.08,35.21L624.91,38.55L625.73,31.34L626.56,31.61L627.39,33.12L628.21,25.29L629.04,27.46L629.87,25.68L630.69,27.97L631.52,25.68L632.35,28.05L634,24.91\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace0bf33765-d198-4100-94c8-76fc0de0739f\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M33.06,225.11L33.89,229.93L34.72,229.93L39.68,230.47L40.5,232.74L45.46,232.74\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M152.09,166.36L152.92,171.83L153.75,171.83L165.32,172.41L166.15,175.65L168.63,178.16L169.45,178.16L171.11,179.75L171.93,183.41L173.59,188.05L174.41,188.05L181.85,188.05L182.68,189.28L190.12,190.01\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M366.18,37.1L367.84,57.18L368.66,57.18L371.14,57.18L371.97,58.21L374.45,60.43L375.28,60.43L379.41,60.61L380.23,61.63L381.89,62.78L382.71,62.78L385.19,67.76L386.02,72.02L387.67,73.56L388.5,73.56L437.27,73.56L438.1,75.66L438.92,75.66L439.75,76.8L440.58,80.83L441.4,81.06L443.88,81.6L444.71,82.55L445.54,83.35L446.36,85.34L448.02,87.91L448.84,87.91L450.5,87.91L451.32,90.85L452.98,92.18L453.8,92.18L457.93,92.18L458.76,93.96L462.07,94.91L462.89,96.29L464.55,103.05L465.37,103.05L471.16,103.05\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace001b46cd-12df-42ef-b39b-3f3395794601\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M15.71,237.8L18.19,236.99L19.01,236.99L32.24,236.99\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M46.29,246.44L47.12,241.57L47.94,241.57L49.6,240.89L50.42,238.47L58.69,237.93L59.51,237.54L62.82,237.54L63.65,236.64L65.3,235.48L66.13,235.23L71.91,235.11L72.74,235.11L74.39,233.39L75.22,233.39L78.53,233.21L79.35,230.76L82.66,224.22L83.49,224.22L90.93,224.22L91.75,222.1L92.58,219.94L93.41,219.94L94.23,219.94L95.06,218.91L96.71,218.27L97.54,218.27L100.84,218.27L101.67,217.32L102.5,214.86L103.32,214.86L104.98,214.86L105.8,213L106.63,211.11L107.46,206.85L109.94,205.74L110.76,205.74L117.38,205.61L118.2,202.57L119.03,200.18L119.86,200.18L121.51,198.89L122.34,198.89L138.87,198.89L139.69,195.51L151.27,195.51\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M190.94,210.51L191.77,208.72L192.6,208.72L193.42,204.64L194.25,204.64L197.56,204.08L198.38,203.13L200.04,201.94L200.86,201.94L210.78,201.94L211.61,199L212.44,191.04L213.26,191.04L257.9,191.04L258.72,185.69L259.55,185.19L260.38,182.06L261.2,175.74L262.03,175.74L264.51,175.74L265.34,173.07L266.16,173.07L266.99,171.6L268.64,167.52L269.47,159.43L271.12,159.43L271.95,143.04L272.78,130.75L273.6,130.75L320.72,130.3L321.55,123.75L322.37,119.6L323.2,119.6L324.03,111.56L324.85,111.56L347.17,111.56L348,108.32L348.82,96.03L349.65,96.03L354.61,96.03L355.44,94.74L358.74,94.74L359.57,93.02L362.88,93.02L363.7,90.65L365.36,90.65\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path class=\"js-line\" d=\"M471.99,134.1L473.64,131.47L474.47,131.47L501.74,131.47L502.57,129.56L504.22,127.76L505.05,125.95L505.88,123.89L506.7,123.89L507.53,123.89L508.36,123.04L509.18,121.35L510.01,115.36L510.84,112.57L511.66,112.57L513.32,109.62L514.14,109.23L514.97,107.91L515.8,103.32L519.1,103.11L519.93,103.11L525.72,103.11L526.54,101.48L527.37,101.48L528.2,99.9L530.68,99.9L531.5,96.89L533.16,96.05L533.98,96.05L535.63,96.05L536.46,92.81L537.29,90.67L538.11,90.67L553.82,90.61L554.65,86.54L556.3,79.04L557.13,79.04L558.78,78.74L559.61,77.52L560.43,73.84L561.26,73.84L562.09,71.59L562.91,71.59L589.36,71.59L590.19,67.1L591.02,63.82L591.84,56.95L593.5,50.28L594.32,50.28L634,50.28\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(115.35,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 5</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(254.22,0)\">Jan 12</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(393.09,0)\">Jan 19</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(531.96,0)\">Jan 26</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" transform=\"translate(0,278.61)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">130</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,239.86)\">140</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,201.11)\">150</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,162.36)\">160</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,123.61)\">170</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,84.86)\">180</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,46.11)\">190</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"smithlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-a1783b\"><g class=\"clips\"/><clipPath id=\"legenda1783b\"><rect width=\"230\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(440,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"230\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(33, 34, 44); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legenda1783b)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.859375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(77.359375,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Short</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"75.4375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(155.296875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">Long</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"71.546875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["st.loc[date_range, (19, 4, 'ETHUSDT')].plot().show_svg()"]}, {"cell_type": "code", "execution_count": 54, "id": "c1ed2c21-e61c-49e1-8b08-1324380ca566", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["377.6 MB\n"]}], "source": ["print(st.getsize())"]}, {"cell_type": "code", "execution_count": 55, "id": "4cc44d67-cabe-41e5-9d4b-c37527ff6dbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["359.1533203125"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["input_size = st.wrapper.shape[0] * st.wrapper.shape[1]\n", "n_outputs = 4\n", "data_type_size = 8\n", "input_size * n_outputs * data_type_size / 1024 / 1024"]}, {"cell_type": "code", "execution_count": 56, "id": "5b150641-4aa6-4634-98a9-8bd698448f4c", "metadata": {}, "outputs": [], "source": ["entries = (~st.superl.isnull()).vbt.signals.fshift()\n", "exits = (~st.supers.isnull()).vbt.signals.fshift()"]}, {"cell_type": "code", "execution_count": 57, "id": "3f5a43d0-9b85-4347-9bb9-f0e9b096d143", "metadata": {}, "outputs": [], "source": ["pf = vbt.Portfolio.from_signals(close, entries, exits, fees=0.001, freq='1h')"]}, {"cell_type": "code", "execution_count": 58, "id": "4a9155ae-4971-4009-b1dc-607dc4116fed", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"572\" height=\"589\" style=\"\" viewBox=\"0 0 572 589\"><rect x=\"0\" y=\"0\" width=\"572\" height=\"589\" style=\"fill: rgb(33, 34, 44); fill-opacity: 1;\"/><defs id=\"defs-fcb247\"><g class=\"clips\"><clipPath id=\"clipfcb247xyplot\" class=\"plotclip\"><rect width=\"425\" height=\"425\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfcb247x\"><rect x=\"57\" y=\"0\" width=\"425\" height=\"589\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfcb247y\"><rect x=\"0\" y=\"30\" width=\"572\" height=\"425\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfcb247xy\"><rect x=\"57\" y=\"30\" width=\"425\" height=\"425\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"gfcb247-cb2e416755-87c6-422b-8fef-388dd5214615\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.666667%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.777778%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"minor-gridlayer\"><g class=\"x\"/><g class=\"y\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(96.84,0)\" d=\"M0,30v425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(229.66,0)\" d=\"M0,30v425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(362.47,0)\" d=\"M0,30v425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,444.88)\" d=\"M57,0h425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,343.69)\" d=\"M57,0h425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,242.5)\" d=\"M57,0h425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,141.31)\" d=\"M57,0h425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,40.12)\" d=\"M57,0h425\" style=\"stroke: rgb(52, 53, 69); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(57,30)\" clip-path=\"url(#clipfcb247xyplot)\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"425\" width=\"425\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"468\" transform=\"translate(96.84,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"468\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(229.66,0)\">10</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"468\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(362.47,0)\">15</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"56\" y=\"4.199999999999999\" transform=\"translate(0,444.88)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"56\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,343.69)\">2.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"56\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,242.5)\">3</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"56\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,141.31)\">3.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"56\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,40.12)\">4</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"smithlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-fcb247\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"slider-container\"><g class=\"slider-group\" transform=\"translate(57,505)\"><text class=\"slider-label\" text-anchor=\"left\" data-notex=\"1\" x=\"0\" y=\"15.600000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">symbol: BTCUSDT</text><rect class=\"slider-rail-rect\" width=\"409\" height=\"5\" rx=\"2\" ry=\"2\" shape-rendering=\"crispEdges\" transform=\"translate(8,32.5)\" style=\"stroke: rgb(33, 34, 44); stroke-opacity: 1; fill: rgb(174, 192, 214); fill-opacity: 1; stroke-width: 1px;\"/><g class=\"slider-labels\"><g class=\"slider-label-group\" transform=\"translate(10,72.6)\"><text class=\"slider-label\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">BTCUSDT</text></g><g class=\"slider-label-group\" transform=\"translate(415,72.6)\"><text class=\"slider-label\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre;\">ETHUSDT</text></g></g><rect class=\"slider-tick-rect\" width=\"0px\" shape-rendering=\"crispEdges\" height=\"7\" transform=\"translate(10,50)\" style=\"fill: rgb(51, 51, 51); fill-opacity: 1;\"/><rect class=\"slider-tick-rect\" width=\"0px\" shape-rendering=\"crispEdges\" height=\"7\" transform=\"translate(415,50)\" style=\"fill: rgb(51, 51, 51); fill-opacity: 1;\"/><rect class=\"slider-rail-touch-rect\" width=\"425\" height=\"46.5\" opacity=\"0\" transform=\"translate(0,25)\" style=\"pointer-events: all; fill: rgb(174, 192, 214); fill-opacity: 1;\"/><rect class=\"slider-grip-rect\" width=\"20\" height=\"20\" rx=\"10\" ry=\"10\" transform=\"translate(0,25)\" style=\"pointer-events: all; stroke: rgb(33, 34, 44); stroke-opacity: 1; fill: rgb(174, 192, 214); fill-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"cb2e416755-87c6-422b-8fef-388dd5214615 colorbar\" transform=\"translate(57,30)\"><rect class=\"cbbg\" x=\"434\" y=\"0\" width=\"69.640625\" height=\"425\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill gradient_filled\" x=\"444\" y=\"0\" width=\"30\" height=\"405\" style=\"fill: url('#gfcb247-cb2e416755-87c6-422b-8fef-388dd5214615');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-30)\"><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" transform=\"translate(0,398.66)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\">0.6</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,348.62)\">0.8</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,298.58)\">1</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,248.54000000000002)\">1.2</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,198.5)\">1.4</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,148.46)\">1.6</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,98.43)\">1.8</text></g><g class=\"ycb2e416755-87c6-422b-8fef-388dd5214615tick\"><text text-anchor=\"start\" x=\"476.9\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(214, 223, 239); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,48.390000000000015)\">2</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-57,-30)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"444\" y=\"10\" width=\"30\" height=\"405\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"269.5\" y=\"494.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(214, 223, 239); opacity: 1; font-weight: normal; white-space: pre;\">st_period</text></g><g class=\"g-ytitle\" transform=\"translate(1.9248046875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.075000000000003,242.5)\" x=\"12.075000000000003\" y=\"242.5\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(214, 223, 239); opacity: 1; font-weight: normal; white-space: pre;\">st_multiplier</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pf.sharpe_ratio.vbt.heatmap(\n", "    x_level='st_period', \n", "    y_level='st_multiplier',\n", "    slider_level='symbol'\n", ").show_svg()"]}, {"cell_type": "code", "execution_count": 59, "id": "855fe67d-6c43-4a9a-9e03-df18d4d2e77c", "metadata": {}, "outputs": [{"data": {"text/plain": ["symbol\n", "BTCUSDT    1.561447\n", "ETHUSDT    2.170813\n", "Name: sharpe_ratio, dtype: float64"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["vbt.Portfolio.from_holding(close, freq='1h').sharpe_ratio"]}, {"cell_type": "markdown", "id": "25a4b932-81a0-42b5-98b5-f58988319952", "metadata": {}, "source": ["## Streaming"]}, {"cell_type": "code", "execution_count": 60, "id": "3a132681-34a2-4a7d-ab67-5cf72645fd7c", "metadata": {}, "outputs": [], "source": ["class SuperTrendAIS(tp.<PERSON>Tuple):\n", "    i: int\n", "    high: float\n", "    low: float\n", "    close: float\n", "    prev_close: float\n", "    prev_upper: float\n", "    prev_lower: float\n", "    prev_dir_: float\n", "    nobs: int\n", "    weighted_avg: float\n", "    old_wt: float\n", "    period: int\n", "    multiplier: float\n", "    \n", "class SuperTrendAOS(tp.NamedTuple):\n", "    nobs: int\n", "    weighted_avg: float\n", "    old_wt: float\n", "    upper: float\n", "    lower: float\n", "    trend: float\n", "    dir_: float\n", "    long: float\n", "    short: float"]}, {"cell_type": "code", "execution_count": 61, "id": "6dca1372-1c55-48f3-b569-7a193142fff6", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def get_tr_one_nb(high, low, prev_close):\n", "    tr0 = abs(high - low)\n", "    tr1 = abs(high - prev_close)\n", "    tr2 = abs(low - prev_close)\n", "    if np.isnan(tr0) or np.isnan(tr1) or np.isnan(tr2):\n", "        tr = np.nan\n", "    else:\n", "        tr = max(tr0, tr1, tr2)\n", "    return tr\n", "\n", "@njit(nogil=True)\n", "def get_med_price_one_nb(high, low):\n", "    return (high + low) / 2\n", "\n", "@njit(nogil=True)\n", "def get_basic_bands_one_nb(high, low, atr, multiplier):\n", "    med_price = get_med_price_one_nb(high, low)\n", "    matr = multiplier * atr\n", "    upper = med_price + matr\n", "    lower = med_price - matr\n", "    return upper, lower\n", "    \n", "@njit(nogil=True)\n", "def get_final_bands_one_nb(close, upper, lower, prev_upper, prev_lower, prev_dir_):\n", "    if close > prev_upper:\n", "        dir_ = 1\n", "    elif close < prev_lower:\n", "        dir_ = -1\n", "    else:\n", "        dir_ = prev_dir_\n", "        if dir_ > 0 and lower < prev_lower:\n", "            lower = prev_lower\n", "        if dir_ < 0 and upper > prev_upper:\n", "            upper = prev_upper\n", "\n", "    if dir_ > 0:\n", "        trend = long = lower\n", "        short = np.nan\n", "    else:\n", "        trend = short = upper\n", "        long = np.nan\n", "    return upper, lower, trend, dir_, long, short"]}, {"cell_type": "code", "execution_count": 62, "id": "e787868d-99b9-41ea-ad97-e70480e021e8", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def superfast_supertrend_acc_nb(in_state):\n", "    i = in_state.i\n", "    high = in_state.high\n", "    low = in_state.low\n", "    close = in_state.close\n", "    prev_close = in_state.prev_close\n", "    prev_upper = in_state.prev_upper\n", "    prev_lower = in_state.prev_lower\n", "    prev_dir_ = in_state.prev_dir_\n", "    nobs = in_state.nobs\n", "    weighted_avg = in_state.weighted_avg\n", "    old_wt = in_state.old_wt\n", "    period = in_state.period\n", "    multiplier = in_state.multiplier\n", "    \n", "    tr = get_tr_one_nb(high, low, prev_close)\n", "\n", "    alpha = vbt.nb.alpha_from_wilder_nb(period)\n", "    ewm_mean_in_state = vbt.nb.EWMMeanAIS(\n", "        i=i,\n", "        value=tr,\n", "        old_wt=old_wt,\n", "        weighted_avg=weighted_avg,\n", "        nobs=nobs,\n", "        alpha=alpha,\n", "        minp=period,\n", "        adjust=False\n", "    )\n", "    ewm_mean_out_state = vbt.nb.ewm_mean_acc_nb(ewm_mean_in_state)\n", "    atr = ewm_mean_out_state.value\n", "    \n", "    upper, lower = get_basic_bands_one_nb(high, low, atr, multiplier)\n", "    \n", "    if i == 0:\n", "        trend, dir_, long, short = np.nan, 1, np.nan, np.nan\n", "    else:\n", "        upper, lower, trend, dir_, long, short = get_final_bands_one_nb(\n", "            close, upper, lower, prev_upper, prev_lower, prev_dir_)\n", "            \n", "    return SuperTrendAOS(\n", "        nobs=ewm_mean_out_state.nobs,\n", "        weighted_avg=ewm_mean_out_state.weighted_avg,\n", "        old_wt=ewm_mean_out_state.old_wt,\n", "        upper=upper,\n", "        lower=lower,\n", "        trend=trend,\n", "        dir_=dir_,\n", "        long=long,\n", "        short=short\n", "    )"]}, {"cell_type": "code", "execution_count": 63, "id": "1d5a76d7-f981-4e64-bf5f-9cd69046cfb2", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def superfast_supertrend_nb(high, low, close, period=7, multiplier=3):\n", "    trend = np.empty(close.shape, dtype=float_)\n", "    dir_ = np.empty(close.shape, dtype=int_)\n", "    long = np.empty(close.shape, dtype=float_)\n", "    short = np.empty(close.shape, dtype=float_)\n", "    \n", "    if close.shape[0] == 0:\n", "        return trend, dir_, long, short\n", "\n", "    nobs = 0\n", "    old_wt = 1.\n", "    weighted_avg = np.nan\n", "    prev_upper = np.nan\n", "    prev_lower = np.nan\n", "\n", "    for i in range(close.shape[0]):\n", "        in_state = SuperTrendAIS(\n", "            i=i,\n", "            high=high[i],\n", "            low=low[i],\n", "            close=close[i],\n", "            prev_close=close[i - 1] if i > 0 else np.nan,\n", "            prev_upper=prev_upper,\n", "            prev_lower=prev_lower,\n", "            prev_dir_=dir_[i - 1] if i > 0 else 1,\n", "            nobs=nobs,\n", "            weighted_avg=weighted_avg,\n", "            old_wt=old_wt,\n", "            period=period,\n", "            multiplier=multiplier\n", "        )\n", "        \n", "        out_state = superfast_supertrend_acc_nb(in_state)\n", "        \n", "        nobs = out_state.nobs\n", "        weighted_avg = out_state.weighted_avg\n", "        old_wt = out_state.old_wt\n", "        prev_upper = out_state.upper\n", "        prev_lower = out_state.lower\n", "        trend[i] = out_state.trend\n", "        dir_[i] = out_state.dir_\n", "        long[i] = out_state.long\n", "        short[i] = out_state.short\n", "        \n", "    return trend, dir_, long, short"]}, {"cell_type": "code", "execution_count": 64, "id": "6a16e5ed-f62c-4523-a5e1-6b75e6af9a7b", "metadata": {}, "outputs": [], "source": ["superfast_out = superfast_supertrend_nb(\n", "    high['BTCUSDT'].values,\n", "    low['BTCUSDT'].values,\n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "code", "execution_count": 65, "id": "94757fa3-9008-4320-b66f-c72c94ab19b6", "metadata": {}, "outputs": [], "source": ["faster_out = faster_supertrend(\n", "    high['BTCUSDT'].values,\n", "    low['BTCUSDT'].values,\n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "code", "execution_count": 66, "id": "f0733554-9a70-4ebb-9962-7ac87ddb63b9", "metadata": {}, "outputs": [], "source": ["np.testing.assert_array_equal(superfast_out[0], faster_out[0])\n", "np.testing.assert_array_equal(superfast_out[1], faster_out[1])\n", "np.testing.assert_array_equal(superfast_out[2], faster_out[2])\n", "np.testing.assert_array_equal(superfast_out[3], faster_out[3])"]}, {"cell_type": "code", "execution_count": 67, "id": "1cc3b596-f88e-4222-947f-2fb1ca0e879c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["183 µs ± 918 ns per loop (mean ± std. dev. of 7 runs, 10,000 loops each)\n"]}], "source": ["%%timeit\n", "superfast_supertrend_nb(\n", "    high['BTCUSDT'].values, \n", "    low['BTCUSDT'].values, \n", "    close['BTCUSDT'].values\n", ")"]}, {"cell_type": "markdown", "id": "08682671-abae-4f68-98d6-da623ed67c7d", "metadata": {}, "source": ["## Multithreading"]}, {"cell_type": "code", "execution_count": 68, "id": "da0c1330-ee15-409c-a57a-a2d95facaa3c", "metadata": {}, "outputs": [], "source": ["SuperTrend = vbt.IF(\n", "    class_name='SuperTrend',\n", "    short_name='st',\n", "    input_names=['high', 'low', 'close'],\n", "    param_names=['period', 'multiplier'],\n", "    output_names=['supert', 'superd', 'superl', 'supers']\n", ").with_apply_func(\n", "    superfast_supertrend_nb, \n", "    takes_1d=True,\n", "    period=7, \n", "    multiplier=3\n", ")"]}, {"cell_type": "code", "execution_count": 69, "id": "72fbbf4b-107f-4efd-9a13-01b3230b8ec0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.92 ms ± 358 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "SuperTrend.run(high, low, close)"]}, {"cell_type": "code", "execution_count": 70, "id": "01592b3c-cba0-4007-82d5-0026ade663c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["250 ms ± 73.4 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "SuperTrend.run(\n", "    high, low, close, \n", "    period=periods, \n", "    multiplier=multipliers,\n", "    param_product=True,\n", "    execute_kwargs=dict(show_progress=False)\n", ")"]}, {"cell_type": "code", "execution_count": 71, "id": "ee578ba8-c32c-4c4e-9da7-e39a92bfcb7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.4017857142857143"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["270 / 336 / 2"]}, {"cell_type": "code", "execution_count": 72, "id": "527a2a56-f5e8-4897-8288-40edeb023639", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["145 ms ± 25.7 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "SuperTrend.run(\n", "    high, low, close, \n", "    period=periods, \n", "    multiplier=multipliers,\n", "    param_product=True,\n", "    execute_kwargs=dict(\n", "        engine='dask', \n", "        chunk_len='auto', \n", "        show_progress=False\n", "    )\n", ")"]}, {"cell_type": "markdown", "id": "ac158ed3-1c7c-498f-843d-09a6d02439c4", "metadata": {}, "source": ["## Pipelines"]}, {"cell_type": "code", "execution_count": 73, "id": "c5bcfaed-2d4c-4929-8eae-3505d682577d", "metadata": {}, "outputs": [], "source": ["def pipeline(data, period=7, multiplier=3):\n", "    high = data.get('High')\n", "    low = data.get('Low')\n", "    close = data.get('Close')\n", "    st = SuperTrend.run(\n", "        high, \n", "        low, \n", "        close, \n", "        period=period, \n", "        multiplier=multiplier\n", "    )\n", "    entries = (~st.superl.isnull()).vbt.signals.fshift()\n", "    exits = (~st.supers.isnull()).vbt.signals.fshift()\n", "    pf = vbt.Portfolio.from_signals(\n", "        close, \n", "        entries=entries, \n", "        exits=exits, \n", "        fees=0.001,\n", "        save_returns=True,\n", "        max_order_records=0,\n", "        freq='1h'\n", "    )\n", "    return pf.sharpe_ratio"]}, {"cell_type": "code", "execution_count": 74, "id": "e57ec5e5-71a3-46fa-a8d1-edc9d77876f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["st_period  st_multiplier  symbol \n", "7          3              BTCUSDT    1.521221\n", "                          ETHUSDT    2.258501\n", "Name: sharpe_ratio, dtype: float64"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["pipeline(data)"]}, {"cell_type": "code", "execution_count": 75, "id": "c87e3133-9238-4cf6-8fb2-518b36e1962d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24.4 ms ± 4.13 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%%timeit\n", "pipeline(data)"]}, {"cell_type": "code", "execution_count": 76, "id": "b3c94c2a-7c02-484f-9b5b-83892cc0d2e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["10752"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["336 * 32"]}, {"cell_type": "code", "execution_count": 77, "id": "b23cfc27-81f4-43d6-9a3c-3e2136db980c", "metadata": {}, "outputs": [], "source": ["op_tree = (product, periods, multipliers)\n", "period_product, multiplier_product = vbt.generate_param_combs(op_tree)\n", "period_product = np.asarray(period_product)\n", "multiplier_product = np.asarray(multiplier_product)"]}, {"cell_type": "code", "execution_count": 78, "id": "d0c220d9-bd6f-48c5-a794-f02d6a6e3eeb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.15 s ± 996 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "pipeline(data, period_product, multiplier_product)"]}, {"cell_type": "markdown", "id": "52529de0-d7a9-4e4e-9a91-4c2449f3405f", "metadata": {}, "source": ["### Chunked pipeline"]}, {"cell_type": "code", "execution_count": 79, "id": "d8413e9c-5fe1-4339-a289-f9d1f7595ce6", "metadata": {}, "outputs": [], "source": ["chunked_pipeline = vbt.chunked(\n", "    size=vbt.LenSizer(arg_query='period', single_type=int),\n", "    arg_take_spec=dict(\n", "        data=None,\n", "        period=vbt.<PERSON>(),\n", "        multiplier=vbt.ChunkSlicer()\n", "    ),\n", "    merge_func=lambda x: pd.concat(x).sort_index()\n", ")(pipeline)"]}, {"cell_type": "code", "execution_count": 80, "id": "bb15c6bc-d73e-4449-bb98-d43432dc9dad", "metadata": {}, "outputs": [{"data": {"text/plain": ["st_period  st_multiplier  symbol \n", "7          3              BTCUSDT    1.521221\n", "                          ETHUSDT    2.258501\n", "Name: sharpe_ratio, dtype: float64"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["chunked_pipeline(data)"]}, {"cell_type": "code", "execution_count": 81, "id": "728cadd0-a62f-49bb-8c86-c24df3642ad2", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "823086f2006941548eec154924eb68a5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["st_period  st_multiplier  symbol \n", "4          2.0            BTCUSDT    0.451699\n", "                          ETHUSDT    1.391032\n", "           2.1            BTCUSDT    0.495387\n", "                          ETHUSDT    1.134741\n", "           2.2            BTCUSDT    0.985946\n", "                          ETHUSDT    0.955616\n", "           2.3            BTCUSDT    1.193179\n", "                          ETHUSDT    1.307505\n", "Name: sharpe_ratio, dtype: float64"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["chunked_pipeline(\n", "    data, \n", "    period_product[:4], \n", "    multiplier_product[:4],\n", "    _n_chunks=2,\n", ")"]}, {"cell_type": "code", "execution_count": 82, "id": "bc3206ff-4b53-459c-91cd-7183193d76f2", "metadata": {}, "outputs": [], "source": ["chunk_meta, tasks = chunked_pipeline(\n", "    data, \n", "    period_product[:4], \n", "    multiplier_product[:4],\n", "    _n_chunks=2,\n", "    _return_raw_chunks=True\n", ")"]}, {"cell_type": "code", "execution_count": 83, "id": "fc340146-43ee-40a2-8bb2-60494469d39e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[ChunkMeta(uuid='6322a67b-fe9f-4c2a-b1ea-93dbdcfff93c', idx=0, start=0, end=2, indices=None),\n", " ChunkMeta(uuid='1f1de2a9-5fa8-4dfc-8f16-88dbf9d01989', idx=1, start=2, end=4, indices=None)]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["chunk_meta"]}, {"cell_type": "code", "execution_count": 84, "id": "f734466d-59f9-49c6-ae2b-b3ff038833df", "metadata": {}, "outputs": [{"data": {"text/plain": ["[(<function __main__.pipeline(data, period=7, multiplier=3)>,\n", "  (<vectorbtpro.data.custom.HDFData at 0x7fb3e9bcfbe0>,\n", "   array([4, 4]),\n", "   array([2. , 2.1])),\n", "  {}),\n", " (<function __main__.pipeline(data, period=7, multiplier=3)>,\n", "  (<vectorbtpro.data.custom.HDFData at 0x7fb3e9bcfbe0>,\n", "   array([4, 4]),\n", "   array([2.2, 2.3])),\n", "  {})]"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["list(tasks)"]}, {"cell_type": "code", "execution_count": 85, "id": "d279086d-743e-4044-9fe7-1d46e74d04e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.75 s ± 34.3 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_pipeline(data, period_product, multiplier_product)"]}, {"cell_type": "code", "execution_count": 86, "id": "5125c3af-b330-47a2-a24d-49e58d37e471", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8.53 s ± 917 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_pipeline(data, period_product, multiplier_product, _chunk_len=1)"]}, {"cell_type": "markdown", "id": "0e6c55bd-6e54-4ebc-bb9a-9f2a2805403d", "metadata": {}, "source": ["### Numba pipeline"]}, {"cell_type": "code", "execution_count": 87, "id": "d8830575-46de-4884-a278-214c258ecc00", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def pipeline_nb(high, low, close, periods=np.array([7]), multipliers=np.array([3]), ann_factor=365):\n", "    sharpe = np.empty(periods.size * close.shape[1], dtype=float_)\n", "    long_entries = np.empty(close.shape, dtype=np.bool_)\n", "    long_exits = np.empty(close.shape, dtype=np.bool_)\n", "    group_lens = np.full(close.shape[1], 1)\n", "    init_cash = 100.\n", "    fees = 0.001\n", "    k = 0\n", "    \n", "    for i in range(periods.size):\n", "        for col in range(close.shape[1]):\n", "            _, _, superl, supers = superfast_supertrend_nb(\n", "                high[:, col], \n", "                low[:, col], \n", "                close[:, col], \n", "                periods[i], \n", "                multipliers[i]\n", "            )\n", "            long_entries[:, col] = vbt.nb.fshift_1d_nb(~np.isnan(superl), fill_value=False)\n", "            long_exits[:, col] = vbt.nb.fshift_1d_nb(~np.isnan(supers), fill_value=False)\n", "            \n", "        sim_out = vbt.pf_nb.from_signals_nb(\n", "            target_shape=close.shape,\n", "            group_lens=group_lens,\n", "            init_cash=init_cash,\n", "            high=high,\n", "            low=low,\n", "            close=close,\n", "            long_entries=long_entries,\n", "            long_exits=long_exits,\n", "            fees=fees,\n", "            save_returns=True\n", "        )\n", "        returns = sim_out.in_outputs.returns\n", "        sharpe[k:k + close.shape[1]] = vbt.ret_nb.sharpe_ratio_nb(returns, ann_factor, ddof=1)\n", "        k += close.shape[1]\n", "        \n", "    return sharpe"]}, {"cell_type": "code", "execution_count": 88, "id": "2eabd3cf-5f03-4cbb-8b1d-6bba6bcbbd3d", "metadata": {}, "outputs": [], "source": ["ann_factor = vbt.pd_acc.returns.get_ann_factor(freq='1h')"]}, {"cell_type": "code", "execution_count": 89, "id": "d5a82762-332a-4b38-9739-0da01d06a02d", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.521221  , 2.25850084])"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    ann_factor=ann_factor\n", ")"]}, {"cell_type": "code", "execution_count": 90, "id": "cd786362-5c17-4d9a-a8b7-77604c47942f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.37 ms ± 7.09 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%%timeit\n", "pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    ann_factor=ann_factor\n", ")"]}, {"cell_type": "code", "execution_count": 91, "id": "1796f5ea-c0de-4249-98e5-ce14c550108f", "metadata": {}, "outputs": [], "source": ["def merge_func(arrs, ann_args, input_columns):\n", "    arr = np.concatenate(arrs)\n", "    param_index = vbt.stack_indexes((\n", "        pd.Index(ann_args['periods']['value'], name='st_period'),\n", "        pd.Index(ann_args['multipliers']['value'], name='st_multiplier')\n", "    ))\n", "    index = vbt.combine_indexes((\n", "        param_index,\n", "        input_columns\n", "    ))\n", "    return pd.Series(arr, index=index)\n", "\n", "nb_chunked = vbt.chunked(\n", "    size=vbt.ArraySizer(arg_query='periods', axis=0),\n", "    arg_take_spec=dict(\n", "        high=None,\n", "        low=None,\n", "        close=None,\n", "        periods=vbt.ArraySlicer(axis=0),\n", "        multipliers=vbt.ArraySlicer(axis=0),\n", "        ann_factor=None\n", "    ),\n", "    merge_func=merge_func,\n", "    merge_kwargs=dict(\n", "        ann_args=vbt.Rep(\"ann_args\")\n", "    )\n", ")\n", "chunked_pipeline_nb = nb_chunked(pipeline_nb)"]}, {"cell_type": "code", "execution_count": 92, "id": "8f0a9058-bc57-4331-b9e5-31fd511c8862", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9624a77366e3430cb52f622f4ea09ee3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["st_period  st_multiplier  symbol \n", "4          2.0            BTCUSDT    0.451699\n", "                          ETHUSDT    1.391032\n", "           2.1            BTCUSDT    0.495387\n", "                          ETHUSDT    1.134741\n", "           2.2            BTCUSDT    0.985946\n", "                          ETHUSDT    0.955616\n", "           2.3            BTCUSDT    1.193179\n", "                          ETHUSDT    1.307505\n", "dtype: float64"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["chunked_pipeline_nb(\n", "    high.values, \n", "    low.values,\n", "    close.values,\n", "    periods=period_product[:4], \n", "    multipliers=multiplier_product[:4],\n", "    ann_factor=ann_factor,\n", "    _n_chunks=2,\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 93, "id": "badba6cb-3405-498d-b366-cd9205721541", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["927 ms ± 162 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 95, "id": "c5208092-e88f-428a-a2da-f43d325ab20e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["177 ms ± 7.47 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _execute_kwargs=dict(engine='dask'),\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "markdown", "id": "b03f9632-ebfd-4073-a80b-a1671a3d3361", "metadata": {}, "source": ["### Contextualized pipeline"]}, {"cell_type": "markdown", "id": "3eded1d5-1a4a-410c-96ab-3f715be479a1", "metadata": {}, "source": ["#### Streaming Sharpe"]}, {"cell_type": "code", "execution_count": 96, "id": "03f8b212-4ae3-404e-90fe-4c44a867e556", "metadata": {}, "outputs": [], "source": ["class RollSharpeAIS(tp.NamedTuple):\n", "    i: int\n", "    ret: float\n", "    pre_window_ret: float\n", "    cumsum: float\n", "    cumsum_sq: float\n", "    nancnt: int\n", "    window: int\n", "    minp: tp.Optional[int]\n", "    ddof: int\n", "    ann_factor: float\n", "    \n", "class RollSharpeAOS(tp.NamedTuple):\n", "    cumsum: float\n", "    cumsum_sq: float\n", "    nancnt: int\n", "    value: float\n", "\n", "@njit(nogil=True)\n", "def rolling_sharpe_acc_nb(in_state):\n", "    mean_in_state = vbt.nb.RollMeanAIS(\n", "        i=in_state.i,\n", "        value=in_state.ret,\n", "        pre_window_value=in_state.pre_window_ret,\n", "        cumsum=in_state.cumsum,\n", "        nancnt=in_state.nancnt,\n", "        window=in_state.window,\n", "        minp=in_state.minp\n", "    )\n", "    mean_out_state = vbt.nb.rolling_mean_acc_nb(mean_in_state)\n", "    \n", "    std_in_state = vbt.nb.RollStdAIS(\n", "        i=in_state.i,\n", "        value=in_state.ret,\n", "        pre_window_value=in_state.pre_window_ret,\n", "        cumsum=in_state.cumsum,\n", "        cumsum_sq=in_state.cumsum_sq,\n", "        nancnt=in_state.nancnt,\n", "        window=in_state.window,\n", "        minp=in_state.minp,\n", "        ddof=in_state.ddof\n", "    )\n", "    std_out_state = vbt.nb.rolling_std_acc_nb(std_in_state)\n", "    \n", "    mean = mean_out_state.value\n", "    std = std_out_state.value\n", "    if std == 0:\n", "        sharpe = np.nan\n", "    else:\n", "        sharpe = mean / std * np.sqrt(in_state.ann_factor)\n", "    return RollSharpeAOS(\n", "        cumsum=std_out_state.cumsum,\n", "        cumsum_sq=std_out_state.cumsum_sq,\n", "        nancnt=std_out_state.nancnt,\n", "        value=sharpe\n", "    )"]}, {"cell_type": "code", "execution_count": 97, "id": "1339d8a8-c186-4fea-abc3-fd926b17149d", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def rolling_sharpe_ratio_nb(returns, window, minp=None, ddof=0, ann_factor=365):\n", "    if window is None:\n", "        window = returns.shape[0]\n", "    if minp is None:\n", "        minp = window\n", "    out = np.empty(returns.shape, dtype=float_)\n", "    \n", "    if returns.shape[0] == 0:\n", "        return out\n", "\n", "    cumsum = 0.\n", "    cumsum_sq = 0.\n", "    nancnt = 0\n", "\n", "    for i in range(returns.shape[0]):\n", "        in_state = RollSharpeAIS(\n", "            i=i,\n", "            ret=returns[i],\n", "            pre_window_ret=returns[i - window] if i - window >= 0 else np.nan,\n", "            cumsum=cumsum,\n", "            cumsum_sq=cumsum_sq,\n", "            nancnt=nancnt,\n", "            window=window,\n", "            minp=minp,\n", "            ddof=ddof,\n", "            ann_factor=ann_factor\n", "        )\n", "        \n", "        out_state = rolling_sharpe_acc_nb(in_state)\n", "        \n", "        cumsum = out_state.cumsum\n", "        cumsum_sq = out_state.cumsum_sq\n", "        nancnt = out_state.nancnt\n", "        out[i] = out_state.value\n", "        \n", "    return out"]}, {"cell_type": "code", "execution_count": 98, "id": "66ff280b-0057-4095-b473-ceee2ee1e0f1", "metadata": {}, "outputs": [], "source": ["returns = close['BTCUSDT'].vbt.to_returns()"]}, {"cell_type": "code", "execution_count": 99, "id": "084c24fc-53b0-4d85-a184-0d3a8503cd00", "metadata": {}, "outputs": [], "source": ["np.testing.assert_allclose(\n", "    rolling_sharpe_ratio_nb(\n", "        returns=returns.values, \n", "        window=10, \n", "        ddof=1, \n", "        ann_factor=ann_factor),\n", "    returns.vbt.returns(freq='1h').rolling_sharpe_ratio(10).values\n", ")"]}, {"cell_type": "markdown", "id": "47c7edb2-f5de-4d7f-99a7-a35e5dcdec46", "metadata": {}, "source": ["#### Callbacks"]}, {"cell_type": "code", "execution_count": 100, "id": "28fba511-1030-4c6b-8592-5a965b8b6b1e", "metadata": {}, "outputs": [], "source": ["class Memory(tp.<PERSON><PERSON><PERSON><PERSON>):\n", "    nobs: tp.<PERSON><PERSON><PERSON>1d\n", "    old_wt: tp.<PERSON>rray1d\n", "    weighted_avg: tp.Array1d\n", "    prev_upper: tp.<PERSON>rray1d\n", "    prev_lower: tp.<PERSON>rray1d\n", "    prev_dir_: tp.Array1d\n", "    cumsum: tp.<PERSON><PERSON>y1d\n", "    cumsum_sq: tp.Array1d\n", "    nancnt: tp.<PERSON><PERSON>y1d\n", "    was_entry: tp.Array1d\n", "    was_exit: tp.Array1d\n", "\n", "@njit(nogil=True)\n", "def pre_sim_func_nb(c):\n", "    memory = Memory(\n", "        nobs=np.full(c.target_shape[1], 0, dtype=int_),\n", "        old_wt=np.full(c.target_shape[1], 1., dtype=float_),\n", "        weighted_avg=np.full(c.target_shape[1], np.nan, dtype=float_),\n", "        prev_upper=np.full(c.target_shape[1], np.nan, dtype=float_),\n", "        prev_lower=np.full(c.target_shape[1], np.nan, dtype=float_),\n", "        prev_dir_=np.full(c.target_shape[1], np.nan, dtype=float_),\n", "        cumsum=np.full(c.target_shape[1], 0., dtype=float_),\n", "        cumsum_sq=np.full(c.target_shape[1], 0., dtype=float_),\n", "        nancnt=np.full(c.target_shape[1], 0, dtype=int_),\n", "        was_entry=np.full(c.target_shape[1], False, dtype=np.bool_),\n", "        was_exit=np.full(c.target_shape[1], False, dtype=np.bool_)\n", "    )\n", "    return (memory,)"]}, {"cell_type": "code", "execution_count": 101, "id": "5a3f8ffd-82d1-4b86-80c5-0d9d4e9a5680", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def order_func_nb(c, memory, period, multiplier):\n", "    is_entry = memory.was_entry[c.col]\n", "    is_exit = memory.was_exit[c.col]\n", "    \n", "    in_state = SuperTrendAIS(\n", "        i=c.i,\n", "        high=c.high[c.i, c.col],\n", "        low=c.low[c.i, c.col],\n", "        close=c.close[c.i, c.col],\n", "        prev_close=c.close[c.i - 1, c.col] if c.i > 0 else np.nan,\n", "        prev_upper=memory.prev_upper[c.col],\n", "        prev_lower=memory.prev_lower[c.col],\n", "        prev_dir_=memory.prev_dir_[c.col],\n", "        nobs=memory.nobs[c.col],\n", "        weighted_avg=memory.weighted_avg[c.col],\n", "        old_wt=memory.old_wt[c.col],\n", "        period=period,\n", "        multiplier=multiplier\n", "    )\n", "\n", "    out_state = superfast_supertrend_acc_nb(in_state)\n", "\n", "    memory.nobs[c.col] = out_state.nobs\n", "    memory.weighted_avg[c.col] = out_state.weighted_avg\n", "    memory.old_wt[c.col] = out_state.old_wt\n", "    memory.prev_upper[c.col] = out_state.upper\n", "    memory.prev_lower[c.col] = out_state.lower\n", "    memory.prev_dir_[c.col] = out_state.dir_\n", "    memory.was_entry[c.col] = not np.isnan(out_state.long)\n", "    memory.was_exit[c.col] = not np.isnan(out_state.short)\n", "    \n", "    in_position = c.position_now > 0\n", "    if is_entry and not in_position:\n", "        size = np.inf\n", "    elif is_exit and in_position:\n", "        size = -np.inf\n", "    else:\n", "        size = 0.\n", "    return vbt.pf_nb.order_nb(\n", "        size=size, \n", "        direction=vbt.pf_enums.Direction.LongOnly,\n", "        fees=0.001\n", "    )"]}, {"cell_type": "code", "execution_count": 102, "id": "73e93b02-3a10-4143-abf4-24ec71afb42f", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def post_segment_func_nb(c, memory, ann_factor):\n", "    for col in range(c.from_col, c.to_col):\n", "        in_state = RollSharpeAIS(\n", "            i=c.i,\n", "            ret=c.last_return[col],\n", "            pre_window_ret=np.nan,\n", "            cumsum=memory.cumsum[col],\n", "            cumsum_sq=memory.cumsum_sq[col],\n", "            nancnt=memory.nancnt[col],\n", "            window=c.i + 1,\n", "            minp=0,\n", "            ddof=1,\n", "            ann_factor=ann_factor\n", "        )\n", "        out_state = rolling_sharpe_acc_nb(in_state)\n", "        memory.cumsum[col] = out_state.cumsum\n", "        memory.cumsum_sq[col] = out_state.cumsum_sq\n", "        memory.nancnt[col] = out_state.nancnt\n", "        c.in_outputs.sharpe[col] = out_state.value"]}, {"cell_type": "code", "execution_count": 103, "id": "fe428781-32c6-4be7-a65a-97827372a31f", "metadata": {}, "outputs": [], "source": ["class InOutputs(tp.<PERSON>Tuple):\n", "    sharpe: tp.<PERSON><PERSON><PERSON>1d\n", "\n", "@njit(nogil=True)\n", "def ctx_pipeline_nb(high, low, close, periods=np.array([7]), multipliers=np.array([3]), ann_factor=365):\n", "    in_outputs = InOutputs(sharpe=np.empty(close.shape[1], dtype=float_))\n", "    sharpe = np.empty(periods.size * close.shape[1], dtype=float_)\n", "    group_lens = np.full(close.shape[1], 1)\n", "    init_cash = 100.\n", "    k = 0\n", "    \n", "    for i in range(periods.size):\n", "        sim_out = vbt.pf_nb.from_order_func_nb(\n", "            target_shape=close.shape,\n", "            group_lens=group_lens,\n", "            cash_sharing=False,\n", "            init_cash=init_cash,\n", "            pre_sim_func_nb=pre_sim_func_nb,\n", "            order_func_nb=order_func_nb,\n", "            order_args=(periods[i], multipliers[i]),\n", "            post_segment_func_nb=post_segment_func_nb,\n", "            post_segment_args=(ann_factor,),\n", "            high=high,\n", "            low=low,\n", "            close=close,\n", "            in_outputs=in_outputs,\n", "            fill_pos_info=False,\n", "            max_order_records=0\n", "        )\n", "        sharpe[k:k + close.shape[1]] = in_outputs.sharpe\n", "        k += close.shape[1]\n", "        \n", "    return sharpe"]}, {"cell_type": "code", "execution_count": 104, "id": "f3a0e59c-b063-442b-bf9c-9007cabac426", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.521221  , 2.25850084])"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["ctx_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    ann_factor=ann_factor\n", ")"]}, {"cell_type": "code", "execution_count": 105, "id": "2876b2d3-c306-4340-80ea-1106d9953f95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21.8 ms ± 166 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%%timeit\n", "ctx_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    ann_factor=ann_factor\n", ")"]}, {"cell_type": "code", "execution_count": 106, "id": "3c85d1df-5094-4664-888e-90f31e0727b9", "metadata": {}, "outputs": [], "source": ["chunked_ctx_pipeline_nb = nb_chunked(ctx_pipeline_nb)"]}, {"cell_type": "code", "execution_count": 107, "id": "3d4ed0b0-975e-4367-b17c-d233142dddb1", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ea93b0e73eb44574bbbc7f9535b9422c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["st_period  st_multiplier  symbol \n", "4          2.0            BTCUSDT    0.451699\n", "                          ETHUSDT    1.391032\n", "           2.1            BTCUSDT    0.495387\n", "                          ETHUSDT    1.134741\n", "           2.2            BTCUSDT    0.985946\n", "                          ETHUSDT    0.955616\n", "           2.3            BTCUSDT    1.193179\n", "                          ETHUSDT    1.307505\n", "dtype: float64"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["chunked_ctx_pipeline_nb(\n", "    high.values, \n", "    low.values,\n", "    close.values,\n", "    periods=period_product[:4], \n", "    multipliers=multiplier_product[:4],\n", "    ann_factor=ann_factor,\n", "    _n_chunks=2,\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 108, "id": "5e837057-bfbf-404f-916a-fb1826cc911a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7.25 s ± 23.7 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_ctx_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 109, "id": "03d97ac5-d061-4b8c-a45e-857c21e7b724", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.6 s ± 207 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_ctx_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _execute_kwargs=dict(engine='dask'),\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "markdown", "id": "c0d07ec5-9650-4db1-a3cd-def9a0950af2", "metadata": {}, "source": ["### Bonus: Own simulator"]}, {"cell_type": "code", "execution_count": 110, "id": "56dcbce3-1df4-4577-8a49-122c231f309f", "metadata": {}, "outputs": [], "source": ["@njit(nogil=True)\n", "def raw_pipeline_nb(high, low, close, periods=np.array([7]), multipliers=np.array([3]), ann_factor=365):\n", "    out = np.empty(periods.size * close.shape[1], dtype=float_)\n", "    \n", "    if close.shape[0] == 0:\n", "        return out\n", "\n", "    for k in range(len(periods)):\n", "        \n", "        for col in range(close.shape[1]):\n", "            nobs = 0\n", "            old_wt = 1.\n", "            weighted_avg = np.nan\n", "            prev_close_ = np.nan\n", "            prev_upper = np.nan\n", "            prev_lower = np.nan\n", "            prev_dir_ = 1\n", "            cumsum = 0.\n", "            cumsum_sq = 0.\n", "            nancnt = 0\n", "            was_entry = False\n", "            was_exit = False\n", "\n", "            init_cash = 100.\n", "            cash = init_cash\n", "            position = 0.\n", "            debt = 0.\n", "            locked_cash = 0.\n", "            free_cash = init_cash\n", "            val_price = np.nan\n", "            value = init_cash\n", "            prev_value = init_cash\n", "            return_ = 0.\n", "\n", "            for i in range(close.shape[0]):\n", "                is_entry = was_entry\n", "                is_exit = was_exit\n", "\n", "                st_in_state = SuperTrendAIS(\n", "                    i=i,\n", "                    high=high[i, col],\n", "                    low=low[i, col],\n", "                    close=close[i, col],\n", "                    prev_close=prev_close_,\n", "                    prev_upper=prev_upper,\n", "                    prev_lower=prev_lower,\n", "                    prev_dir_=prev_dir_,\n", "                    nobs=nobs,\n", "                    weighted_avg=weighted_avg,\n", "                    old_wt=old_wt,\n", "                    period=periods[k],\n", "                    multiplier=multipliers[k]\n", "                )\n", "\n", "                st_out_state = superfast_supertrend_acc_nb(st_in_state)\n", "\n", "                nobs = st_out_state.nobs\n", "                weighted_avg = st_out_state.weighted_avg\n", "                old_wt = st_out_state.old_wt\n", "                prev_close_ = close[i, col]\n", "                prev_upper = st_out_state.upper\n", "                prev_lower = st_out_state.lower\n", "                prev_dir_ = st_out_state.dir_\n", "                was_entry = not np.isnan(st_out_state.long)\n", "                was_exit = not np.isnan(st_out_state.short)\n", "\n", "                if is_entry and position == 0:\n", "                    size = np.inf\n", "                elif is_exit and position > 0:\n", "                    size = -np.inf\n", "                else:\n", "                    size = np.nan\n", "\n", "                val_price = close[i, col]\n", "                value = cash + position * val_price\n", "                if not np.isnan(size):\n", "                    exec_state = vbt.pf_enums.ExecState(\n", "                        cash=cash,\n", "                        position=position,\n", "                        debt=debt,\n", "                        locked_cash=locked_cash,\n", "                        free_cash=free_cash,\n", "                        val_price=val_price,\n", "                        value=value\n", "                    )\n", "                    price_area = vbt.pf_enums.PriceArea(\n", "                        open=np.nan,\n", "                        high=high[i, col],\n", "                        low=low[i, col],\n", "                        close=close[i, col]\n", "                    )\n", "                    order = vbt.pf_nb.order_nb(\n", "                        size=size, \n", "                        direction=vbt.pf_enums.Direction.LongOnly,\n", "                        fees=0.001\n", "                    )\n", "                    _, new_exec_state = vbt.pf_nb.execute_order_nb(exec_state, order, price_area)\n", "                    cash, position, debt, locked_cash, free_cash, val_price, value = new_exec_state\n", "\n", "                value = cash + position * val_price\n", "                return_ = vbt.ret_nb.get_return_nb(prev_value, value)\n", "                prev_value = value\n", "\n", "                sharpe_in_state = RollSharpeAIS(\n", "                    i=i,\n", "                    ret=return_,\n", "                    pre_window_ret=np.nan,\n", "                    cumsum=cumsum,\n", "                    cumsum_sq=cumsum_sq,\n", "                    nancnt=nancnt,\n", "                    window=i + 1,\n", "                    minp=0,\n", "                    ddof=1,\n", "                    ann_factor=ann_factor\n", "                )\n", "                sharpe_out_state = rolling_sharpe_acc_nb(sharpe_in_state)\n", "                cumsum = sharpe_out_state.cumsum\n", "                cumsum_sq = sharpe_out_state.cumsum_sq\n", "                nancnt = sharpe_out_state.nancnt\n", "                sharpe = sharpe_out_state.value\n", "\n", "            out[k * close.shape[1] + col] = sharpe\n", "        \n", "    return out"]}, {"cell_type": "code", "execution_count": 111, "id": "99f0e6a6-68ca-4d6e-8160-a9dfa5fc6153", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.521221  , 2.25850084])"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["raw_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    ann_factor=ann_factor\n", ")"]}, {"cell_type": "code", "execution_count": 112, "id": "6e16670e-ecd3-4d94-b926-e7773eb088dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["871 µs ± 97.9 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n"]}], "source": ["%%timeit\n", "raw_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    ann_factor=ann_factor\n", ")"]}, {"cell_type": "code", "execution_count": 113, "id": "14812b10-2e4f-4171-ba91-eecd5c329394", "metadata": {}, "outputs": [], "source": ["chunked_raw_pipeline_nb = nb_chunked(raw_pipeline_nb)"]}, {"cell_type": "code", "execution_count": 114, "id": "8339de75-6c3c-4e59-9df0-17f969e8d14c", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9c5535d56d574a8eb62e97cac8132f87", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["st_period  st_multiplier  symbol \n", "4          2.0            BTCUSDT    0.451699\n", "                          ETHUSDT    1.391032\n", "           2.1            BTCUSDT    0.495387\n", "                          ETHUSDT    1.134741\n", "           2.2            BTCUSDT    0.985946\n", "                          ETHUSDT    0.955616\n", "           2.3            BTCUSDT    1.193179\n", "                          ETHUSDT    1.307505\n", "dtype: float64"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["chunked_raw_pipeline_nb(\n", "    high.values, \n", "    low.values,\n", "    close.values,\n", "    periods=period_product[:4], \n", "    multipliers=multiplier_product[:4],\n", "    ann_factor=ann_factor,\n", "    _n_chunks=2,\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 115, "id": "1fa1ac51-3cea-4ae8-86dc-d2b5068994b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["268 ms ± 30.7 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "chunked_raw_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 116, "id": "d8115735-80ca-463e-ba6d-f0892e413b17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["62.4 ms ± 3.59 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%%timeit\n", "chunked_raw_pipeline_nb(\n", "    high.values, \n", "    low.values, \n", "    close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _execute_kwargs=dict(engine=\"dask\"),\n", "    _merge_kwargs=dict(input_columns=close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 117, "id": "382e71b5-5cbc-46fa-b2de-ce031ad902a0", "metadata": {}, "outputs": [], "source": ["range_len = int(vbt.timedelta('365d') / vbt.timedelta('1h'))"]}, {"cell_type": "code", "execution_count": 118, "id": "dabf85c8-62ac-4a93-8903-2255e66b2d09", "metadata": {}, "outputs": [], "source": ["splitter = vbt.Splitter.from_n_rolling(high.index, n=100, length=range_len)\n", "\n", "roll_high = splitter.take(high, into=\"reset_stacked\")\n", "roll_low = splitter.take(low, into=\"reset_stacked\")\n", "roll_close = splitter.take(close, into=\"reset_stacked\")\n", "\n", "range_indexes = splitter.take(high.index)"]}, {"cell_type": "code", "execution_count": 119, "id": "722b1a84-5307-42fa-97f1-0027301e4367", "metadata": {}, "outputs": [{"data": {"text/plain": ["MultiIndex([( 0, 'BTCUSDT'),\n", "            ( 0, 'ETHUSDT'),\n", "            ( 1, 'BTCUSDT'),\n", "            ( 1, 'ETHUSDT'),\n", "            ( 2, 'BTCUSDT'),\n", "            ( 2, 'ETHUSDT'),\n", "            ( 3, 'BTCUSDT'),\n", "            ( 3, 'ETHUSDT'),\n", "            ( 4, 'BTCUSDT'),\n", "            ( 4, 'ETHUSDT'),\n", "            ...\n", "            (95, 'BTCUSDT'),\n", "            (95, 'ETHUSDT'),\n", "            (96, 'BTCUSDT'),\n", "            (96, 'ETHUSDT'),\n", "            (97, 'BTCUSDT'),\n", "            (97, 'ETHUSDT'),\n", "            (98, 'BTCUSDT'),\n", "            (98, 'ETHUSDT'),\n", "            (99, 'BTCUSDT'),\n", "            (99, 'ETHUSDT')],\n", "           names=['split', 'symbol'], length=200)"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["roll_close.columns"]}, {"cell_type": "code", "execution_count": 120, "id": "5832339d-885a-4daa-982e-22b3c665d45c", "metadata": {}, "outputs": [{"data": {"text/plain": ["DatetimeIndex(['2020-01-01 00:00:00+00:00', '2020-01-01 01:00:00+00:00',\n", "               '2020-01-01 02:00:00+00:00', '2020-01-01 03:00:00+00:00',\n", "               '2020-01-01 04:00:00+00:00', '2020-01-01 05:00:00+00:00',\n", "               '2020-01-01 06:00:00+00:00', '2020-01-01 07:00:00+00:00',\n", "               '2020-01-01 08:00:00+00:00', '2020-01-01 09:00:00+00:00',\n", "               ...\n", "               '2020-12-31 08:00:00+00:00', '2020-12-31 09:00:00+00:00',\n", "               '2020-12-31 10:00:00+00:00', '2020-12-31 11:00:00+00:00',\n", "               '2020-12-31 12:00:00+00:00', '2020-12-31 13:00:00+00:00',\n", "               '2020-12-31 14:00:00+00:00', '2020-12-31 15:00:00+00:00',\n", "               '2020-12-31 16:00:00+00:00', '2020-12-31 17:00:00+00:00'],\n", "              dtype='datetime64[ns, UTC]', name='Open time', length=8760, freq=None)"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["range_indexes[0]"]}, {"cell_type": "code", "execution_count": 121, "id": "5232615f-9745-48d4-8cea-3e32d19d4c77", "metadata": {}, "outputs": [], "source": ["sharpe_ratios = chunked_raw_pipeline_nb(\n", "    roll_high.values, \n", "    roll_low.values,\n", "    roll_close.values,\n", "    periods=period_product, \n", "    multipliers=multiplier_product,\n", "    ann_factor=ann_factor,\n", "    _execute_kwargs=dict(engine=\"dask\"),\n", "    _merge_kwargs=dict(input_columns=roll_close.columns)\n", ")"]}, {"cell_type": "code", "execution_count": 122, "id": "190dd192-7fc0-48e1-8ad9-c9653c9c01ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["st_period  st_multiplier  split  symbol \n", "4          2.0            0      BTCUSDT    1.751331\n", "                                 ETHUSDT    2.479750\n", "                          1      BTCUSDT    1.847095\n", "                                 ETHUSDT    2.736193\n", "                          2      BTCUSDT    1.739149\n", "                                              ...   \n", "19         4.0            97     ETHUSDT    1.503001\n", "                          98     BTCUSDT    0.954932\n", "                                 ETHUSDT    1.204134\n", "                          99     BTCUSDT    0.818209\n", "                                 ETHUSDT    1.191223\n", "Length: 67200, dtype: float64"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["sharpe_ratios"]}, {"cell_type": "code", "execution_count": 123, "id": "077421ca-b5d6-4e22-b53b-258c4033a8c0", "metadata": {}, "outputs": [], "source": ["pf_hold = vbt.Portfolio.from_holding(roll_close, freq='1h')\n", "sharpe_ratios_hold = pf_hold.sharpe_ratio"]}, {"cell_type": "code", "execution_count": 124, "id": "838776aa-3700-4d86-921b-d0bbabbdb47e", "metadata": {}, "outputs": [{"data": {"text/plain": ["split  symbol \n", "0      BTCUSDT    2.229122\n", "       ETHUSDT    2.370132\n", "1      BTCUSDT    2.298050\n", "       ETHUSDT    2.611722\n", "2      BTCUSDT    2.351417\n", "                    ...   \n", "97     ETHUSDT    2.315863\n", "98     BTCUSDT    1.124489\n", "       ETHUSDT    2.114297\n", "99     BTCUSDT    0.975638\n", "       ETHUSDT    2.008839\n", "Name: sharpe_ratio, Length: 200, dtype: float64"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["sharpe_ratios_hold"]}, {"cell_type": "code", "execution_count": 125, "id": "7e8a6477-3235-4243-bb93-662d17daf28d", "metadata": {}, "outputs": [], "source": ["def plot_subperiod_sharpe(index, sharpe_ratios, sharpe_ratios_hold, range_indexes, symbol):\n", "    split = index[0]\n", "    sharpe_ratios = sharpe_ratios.xs(\n", "        symbol, \n", "        level='symbol', \n", "        drop_level=True)\n", "    sharpe_ratios = sharpe_ratios.xs(\n", "        split, \n", "        level='split', \n", "        drop_level=True)\n", "    start_date = range_indexes[split][0]\n", "    end_date = range_indexes[split][-1]\n", "    return sharpe_ratios.vbt.heatmap(\n", "        x_level='st_period', \n", "        y_level='st_multiplier',\n", "        title=\"{} - {}\".format(\n", "            start_date.strftime(\"%d %b, %Y %H:%M:%S\"),\n", "            end_date.strftime(\"%d %b, %Y %H:%M:%S\")\n", "        ),\n", "        trace_kwargs=dict(\n", "            zmin=sharpe_ratios.min(),\n", "            zmid=sharpe_ratios_hold[(split, symbol)],\n", "            zmax=sharpe_ratios.max(),\n", "            colorscale='Spectral'\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": 126, "id": "5306f36f-765f-4d52-b8a8-b4c3a4ba7b6d", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fd3a3dd2cf5749ff83f9279c2135b167", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fname = 'raw_pipeline.gif'\n", "level_idx = sharpe_ratios.index.names.index('split')\n", "split_indices = sharpe_ratios.index.levels[level_idx]\n", "\n", "vbt.save_animation(\n", "    fname,\n", "    split_indices, \n", "    plot_subperiod_sharpe,\n", "    sharpe_ratios,\n", "    sharpe_ratios_hold,\n", "    range_indexes,\n", "    'BTCUSDT',\n", "    delta=1,\n", "    fps=7,\n", "    writer_kwargs=dict(loop=0)\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dd826ac3-a4da-4065-a5d0-9c77ea5ad6f0", "metadata": {}, "outputs": [], "source": ["from IPython.display import Image, display\n", "    \n", "with open(fname,'rb') as f:\n", "    display(Image(data=f.read(), format='png'))"]}, {"cell_type": "code", "execution_count": null, "id": "4743b6d1-c6a1-4fa7-85f0-0c1e7366a44c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}