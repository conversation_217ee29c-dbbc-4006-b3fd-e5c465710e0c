[build-system]
requires = ["setuptools>=64"]
build-backend = "setuptools.build_meta"

[project]
name = "vectorbtpro"
version = "2025.3.1"
description = "Next-Generation Quantitative Analysis Tool"
license = { file = "LICENSE" }
authors = [{ name = "<PERSON><PERSON>", email = "<EMAIL>" }]
readme = "README.md"
requires-python = "~=3.8"
dependencies = [
    "numpy>=1.20.0",
    "pandas>=1.5.0",
    "numba>=0.53.1, <0.57.0; python_version<'3.10'",
    "numba>=0.56.0, <0.57.0; python_version>='3.10' and python_version<'3.11'",
    "numba>=0.57.0; python_version>='3.11'",
    "scipy",
    "scikit-learn",
    "schedule",
    "requests",
    "tqdm",
    "python-dateutil",
    "dateparser",
    "imageio",
    "typing_extensions",
    "mypy_extensions",
    "humanize",
    "attrs>=21.1.0",
    "websocket-client",
    "graphlib_backport; python_version<'3.9'",
    "tomli>=1.1.0; python_version<'3.11'",
    "astor; python_version<'3.9'"
]
classifiers = [
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Information Technology",
    "Intended Audience :: Science/Research",
    "License :: Other/Proprietary License",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
    "Topic :: Software Development",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Information Analysis"
]

[project.urls]
homepage = "https://vectorbt.pro/"
documentation = "https://vectorbt.pro/documentation/"
repository = "https://github.com/polakowo/vectorbt.pro"

[project.optional-dependencies]
data-base = [
    "tables",
    "SQLAlchemy>=2.0.0",
    "duckdb",
    "pyarrow",
    "yfinance>=0.2.54",
]
data = [
    "vectorbtpro[data-base]",
    "duckdb-engine",
    "fastparquet",
    "python-binance>=1.0.16",
    "alpaca-py",
    "ccxt>=1.89.14",
    "polygon-api-client>=1.0.0",
    "beautifulsoup4",
    "nasdaq-data-link",
    "alpha_vantage>=3.0.0",
    "databento",
    "findatapy",
]
ind-base = [
    "TA-Lib"
]
ind = [
    "vectorbtpro[ind-base]",
    "ta",
    "pandas_ta",
    "technical",
    "smartmoneyconcepts"
]
perf-base = [
    "numexpr"
]
perf = [
    "vectorbtpro[perf-base]",
    "Bottleneck"
]
opt-base = [
    "hyperopt",
    "optuna"
]
opt = [
    "vectorbtpro[opt-base]"
]
exec-base = [
    "dask",
    "pathos"
]
exec = [
    "vectorbtpro[exec-base]",
    "mpire",
    "ray>=1.4.1"
]
plot-base = [
    "matplotlib>=3.2.0",
    "plotly>=5.0.0",
    "ipywidgets>=7.0.0",
    "kaleido",
]
plot = [
    "vectorbtpro[plot-base]",
    "plotly-resampler"
]
pfopt-base = [
    "PyPortfolioOpt>=1.5.1",
    "Riskfolio-Lib>=3.3.0"
]
pfopt = [
    "vectorbtpro[pfopt-base]",
    "universal-portfolios"
]
stats-base = [
    "quantstats>=0.0.37"
]
stats = [
    "vectorbtpro[stats-base]"
]
msg = [
    "python-telegram-bot"
]
io-base = [
    "dill",
    "blosc2"
]
io = [
    "vectorbtpro[io-base]",
    "lz4",
    "pyperclip"
]
knwl-base = [
    "tabulate",
    "ruamel.yaml",
    "markdown",
    "Pygments",
    "openai",
    "tiktoken",
    "lmdbm",
    "bm25s",
    "PyStemmer",
]
knwl = [
    "vectorbtpro[knwl-base]",
    "PyGithub>=1.59.0",
    "jmespath",
    "jsonpath-ng",
    "fuzzysearch",
    "rapidfuzz",
    "nestedtext",
    "toml",
    "PyYAML",
    "litellm",
    "llama_index"
]
base = [
    "vectorbtpro[data-base,ind-base,perf-base,opt-base,exec-base,plot-base,pfopt-base,stats-base,io-base,knwl-base]"
]
base-no-talib = [
    "vectorbtpro[data-base,perf-base,opt-base,exec-base,plot-base,pfopt-base,stats-base,io-base,knwl-base]"
]
all = [
    "vectorbtpro[data,ind,perf,opt,exec,plot,pfopt,stats,msg,io,knwl]"
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
include = ["vectorbtpro", "vectorbtpro.*"]
namespaces = false
