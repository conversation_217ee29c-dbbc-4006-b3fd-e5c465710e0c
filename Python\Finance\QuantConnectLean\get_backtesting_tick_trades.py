import os
import requests
from datetime import datetime, timedelta
from tqdm import tqdm

# Define the base URL and date range
base_url = "https://data.binance.vision/data/spot/daily/trades/DOGEFDUSD/DOGEFDUSD-trades-{}.zip"
start_date = datetime(2025, 5, 1)
end_date = datetime(2025, 5, 16)
output_dir = r"D:\work\xstarwalker168\Python\Finance\QuantConnectLean\Data\crypto\binance\tick\dogefdusd"

# Ensure output directory exists
os.makedirs(output_dir, exist_ok=True)

# Generate dates and download files
current_date = start_date
while current_date <= end_date:
    # Format date for URL and filename
    date_str = current_date.strftime("%Y-%m-%d")
    filename_date = current_date.strftime("%Y%m%d")
    url = base_url.format(date_str)
    output_filename = f"{filename_date}_trade.zip"
    output_path = os.path.join(output_dir, output_filename)

    # Download the file with progress bar
    try:
        print(f"\nDownloading {url}")
        response = requests.get(url, stream=True)

        # Check if the request was successful
        if response.status_code == 200:
            # Get total file size if available
            total_size = int(response.headers.get("content-length", 0))

            # Initialize progress bar
            progress_bar = tqdm(total=total_size, unit="B", unit_scale=True, desc=output_filename)

            # Write file with progress updates
            with open(output_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        progress_bar.update(len(chunk))

            progress_bar.close()
            print(f"Saved as {output_path}")
        else:
            print(f"Failed to download {url}. Status code: {response.status_code}")

    except Exception as e:
        print(f"Error downloading {url}: {str(e)}")

    # Move to next date
    current_date += timedelta(days=1)
